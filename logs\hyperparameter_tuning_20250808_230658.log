2025-08-08 23:06:58 - hyperparameter_tuning - INFO - 开始对 DecisionTree 进行超参数调优，试验次数: 100
2025-08-08 23:06:58 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 6
2025-08-08 23:06:58 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-08 23:06:58 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-08 23:06:58 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-08 23:06:58 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 100, 'n_jobs': 6, 'timeout': 1800}
2025-08-08 23:06:58 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9125
2025-08-08 23:06:58 - hyperparameter_tuning - INFO - Trial 4: 发现更好的得分 0.9279
2025-08-08 23:06:58 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9411
2025-08-08 23:06:58 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:06:58 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:06:58 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9411
2025-08-08 23:06:58 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9411
2025-08-08 23:06:58 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:06:58 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9411
2025-08-08 23:06:58 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:06:58 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9411
2025-08-08 23:06:58 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:06:58 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:06:58 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9411
2025-08-08 23:06:58 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9411
2025-08-08 23:06:58 - hyperparameter_tuning - INFO - 模型 DecisionTree 的最佳参数: {'max_depth': 4, 'min_samples_split': 23, 'min_samples_leaf': 19, 'criterion': 'gini', 'class_weight': 'balanced', 'max_features': None}
2025-08-08 23:06:58 - hyperparameter_tuning - INFO - 模型 DecisionTree 的最佳得分: 0.9411
2025-08-08 23:06:58 - hyperparameter_tuning - INFO - 实际执行试验次数: 18/100
2025-08-08 23:06:58 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-08 23:06:59 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 23:06:59 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\DecisionTree\optimization_history_20250808_230658.html
2025-08-08 23:06:59 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 23:06:59 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\DecisionTree\param_importances_20250808_230659.html
2025-08-08 23:06:59 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.06 秒
2025-08-08 23:06:59 - hyperparameter_tuning - INFO - 开始对 RandomForest 进行超参数调优，试验次数: 100
2025-08-08 23:06:59 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 6
2025-08-08 23:06:59 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-08 23:06:59 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-08 23:06:59 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-08 23:06:59 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 100, 'n_jobs': 6, 'timeout': 1800}
2025-08-08 23:07:01 - hyperparameter_tuning - INFO - Trial 3: 发现更好的得分 0.9827
2025-08-08 23:07:08 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:07:08 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9836
2025-08-08 23:07:08 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:07:08 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9836
2025-08-08 23:07:09 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:07:09 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9836
2025-08-08 23:07:09 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:07:09 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9836
2025-08-08 23:07:09 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:07:09 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9836
2025-08-08 23:07:10 - hyperparameter_tuning - INFO - Trial 15: 发现更好的得分 0.9853
2025-08-08 23:07:10 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳参数: {'n_estimators': 295, 'max_depth': 2, 'min_samples_split': 2, 'min_samples_leaf': 1, 'max_features': 'sqrt'}
2025-08-08 23:07:10 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳得分: 0.9853
2025-08-08 23:07:10 - hyperparameter_tuning - INFO - 实际执行试验次数: 16/100
2025-08-08 23:07:10 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-08 23:07:10 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 23:07:10 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\optimization_history_20250808_230710.html
2025-08-08 23:07:11 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 23:07:11 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\param_importances_20250808_230710.html
2025-08-08 23:07:11 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 11.67 秒
2025-08-08 23:07:11 - hyperparameter_tuning - INFO - 开始对 XGBoost 进行超参数调优，试验次数: 100
2025-08-08 23:07:11 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 6
2025-08-08 23:07:11 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-08 23:07:11 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-08 23:07:11 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-08 23:07:11 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 100, 'n_jobs': 6, 'timeout': 1800}
2025-08-08 23:07:11 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 23:07:11 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 23:07:11 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 23:07:11 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 23:07:11 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 23:07:11 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 23:07:11 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9851
2025-08-08 23:07:11 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 23:07:11 - hyperparameter_tuning - INFO - Trial 2: 发现更好的得分 0.9866
2025-08-08 23:07:11 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 23:07:11 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 23:07:12 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 23:07:12 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9884
2025-08-08 23:07:12 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 23:07:12 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 23:07:12 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 23:07:12 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 23:07:12 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 23:07:12 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 23:07:12 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 23:07:13 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 23:07:13 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 23:07:13 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 23:07:13 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:07:13 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9891
2025-08-08 23:07:13 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:07:13 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9891
2025-08-08 23:07:13 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:07:13 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9891
2025-08-08 23:07:13 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:07:13 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9892
2025-08-08 23:07:14 - hyperparameter_tuning - INFO - Trial 19: 发现更好的得分 0.9899
2025-08-08 23:07:14 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳参数: {'n_estimators': 289, 'max_depth': 4, 'learning_rate': 0.2422517531288597, 'subsample': 0.6595624743036407, 'colsample_bytree': 0.993646027075368}
2025-08-08 23:07:14 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳得分: 0.9899
2025-08-08 23:07:14 - hyperparameter_tuning - INFO - 实际执行试验次数: 20/100
2025-08-08 23:07:14 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-08 23:07:14 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 23:07:14 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\XGBoost\optimization_history_20250808_230714.html
2025-08-08 23:07:14 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 23:07:14 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\XGBoost\param_importances_20250808_230714.html
2025-08-08 23:07:14 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 3.67 秒
2025-08-08 23:07:14 - hyperparameter_tuning - INFO - 开始对 LightGBM 进行超参数调优，试验次数: 100
2025-08-08 23:07:14 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 6
2025-08-08 23:07:14 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-08 23:07:14 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-08 23:07:14 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-08 23:07:14 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 100, 'n_jobs': 6, 'timeout': 1800}
2025-08-08 23:07:21 - hyperparameter_tuning - INFO - Trial 4: 发现更好的得分 0.9859
2025-08-08 23:07:21 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:07:21 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9867
2025-08-08 23:07:21 - hyperparameter_tuning - INFO - Trial 11: 发现更好的得分 0.9875
2025-08-08 23:07:22 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳参数: {'n_estimators': 159, 'max_depth': 10, 'learning_rate': 0.17905165423348202, 'feature_fraction': 0.8204385626360735, 'bagging_fraction': 0.6590122802431319}
2025-08-08 23:07:22 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳得分: 0.9875
2025-08-08 23:07:22 - hyperparameter_tuning - INFO - 实际执行试验次数: 16/100
2025-08-08 23:07:22 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-08 23:07:22 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 23:07:22 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\LightGBM\optimization_history_20250808_230722.html
2025-08-08 23:07:22 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 23:07:22 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\LightGBM\param_importances_20250808_230722.html
2025-08-08 23:07:22 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 8.12 秒
2025-08-08 23:07:22 - hyperparameter_tuning - INFO - 开始对 CatBoost 进行超参数调优，试验次数: 100
2025-08-08 23:07:22 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 6
2025-08-08 23:07:22 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-08 23:07:22 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-08 23:07:22 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-08 23:07:22 - hyperparameter_tuning - INFO - CatBoost使用串行模式避免GPU设备冲突
2025-08-08 23:07:22 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 100, 'n_jobs': 1, 'timeout': 1800}
2025-08-08 23:07:27 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9721
2025-08-08 23:07:28 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9820
2025-08-08 23:07:42 - hyperparameter_tuning - INFO - Trial 6: 发现更好的得分 0.9861
2025-08-08 23:08:28 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:08:28 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9869
2025-08-08 23:08:28 - hyperparameter_tuning - INFO - 模型 CatBoost 的最佳参数: {'iterations': 204, 'depth': 2, 'learning_rate': 0.11895186568849454, 'l2_leaf_reg': 8.448363213508443, 'bagging_temperature': 0.8095789183531145}
2025-08-08 23:08:28 - hyperparameter_tuning - INFO - 模型 CatBoost 的最佳得分: 0.9869
2025-08-08 23:08:28 - hyperparameter_tuning - INFO - 实际执行试验次数: 17/100
2025-08-08 23:08:28 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-08 23:08:28 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 23:08:28 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\CatBoost\optimization_history_20250808_230828.html
2025-08-08 23:08:28 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 23:08:28 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\CatBoost\param_importances_20250808_230828.html
2025-08-08 23:08:28 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 65.92 秒
2025-08-08 23:08:28 - hyperparameter_tuning - INFO - 开始对 Logistic 进行超参数调优，试验次数: 100
2025-08-08 23:08:28 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 6
2025-08-08 23:08:28 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-08 23:08:28 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-08 23:08:28 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-08 23:08:28 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 100, 'n_jobs': 6, 'timeout': 1800}
2025-08-08 23:08:28 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9731
2025-08-08 23:08:29 - hyperparameter_tuning - INFO - Trial 3: 发现更好的得分 0.9747
2025-08-08 23:08:29 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:08:29 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9747
2025-08-08 23:08:29 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:08:29 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9747
2025-08-08 23:08:29 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:08:29 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9747
2025-08-08 23:08:29 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:08:29 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9747
2025-08-08 23:08:29 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:08:29 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:08:29 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9747
2025-08-08 23:08:29 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9747
2025-08-08 23:08:29 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳参数: {'clf__C': 8.884615579781006, 'clf__solver': 'lbfgs'}
2025-08-08 23:08:29 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳得分: 0.9747
2025-08-08 23:08:29 - hyperparameter_tuning - INFO - 实际执行试验次数: 21/100
2025-08-08 23:08:29 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-08 23:08:29 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 23:08:29 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\Logistic\optimization_history_20250808_230829.html
2025-08-08 23:08:30 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 23:08:30 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\Logistic\param_importances_20250808_230830.html
2025-08-08 23:08:30 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.43 秒
2025-08-08 23:08:30 - hyperparameter_tuning - INFO - 开始对 SVM 进行超参数调优，试验次数: 100
2025-08-08 23:08:30 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 6
2025-08-08 23:08:30 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-08 23:08:30 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-08 23:08:30 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-08 23:08:30 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 100, 'n_jobs': 6, 'timeout': 1800}
2025-08-08 23:08:30 - hyperparameter_tuning - INFO - Trial 4: 发现更好的得分 0.9859
2025-08-08 23:08:30 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:08:30 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9859
2025-08-08 23:08:30 - hyperparameter_tuning - INFO - Trial 11: 发现更好的得分 0.9876
2025-08-08 23:08:30 - hyperparameter_tuning - INFO - 模型 SVM 的最佳参数: {'clf__C': 4.265370127923194, 'clf__kernel': 'rbf'}
2025-08-08 23:08:30 - hyperparameter_tuning - INFO - 模型 SVM 的最佳得分: 0.9876
2025-08-08 23:08:30 - hyperparameter_tuning - INFO - 实际执行试验次数: 16/100
2025-08-08 23:08:30 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-08 23:08:31 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 23:08:31 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\SVM\optimization_history_20250808_230830.html
2025-08-08 23:08:31 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 23:08:31 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\SVM\param_importances_20250808_230831.html
2025-08-08 23:08:31 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.16 秒
2025-08-08 23:08:31 - hyperparameter_tuning - INFO - 开始对 KNN 进行超参数调优，试验次数: 100
2025-08-08 23:08:31 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 6
2025-08-08 23:08:31 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-08 23:08:31 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-08 23:08:31 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-08 23:08:31 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 100, 'n_jobs': 6, 'timeout': 1800}
2025-08-08 23:08:31 - hyperparameter_tuning - INFO - Trial 2: 发现更好的得分 0.9812
2025-08-08 23:08:31 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:08:31 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9816
2025-08-08 23:08:31 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:08:31 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9816
2025-08-08 23:08:31 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:08:31 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9816
2025-08-08 23:08:31 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:08:31 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9816
2025-08-08 23:08:31 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:08:31 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9816
2025-08-08 23:08:31 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:08:31 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9816
2025-08-08 23:08:31 - hyperparameter_tuning - INFO - 模型 KNN 的最佳参数: {'clf__n_neighbors': 4}
2025-08-08 23:08:31 - hyperparameter_tuning - INFO - 模型 KNN 的最佳得分: 0.9816
2025-08-08 23:08:31 - hyperparameter_tuning - INFO - 实际执行试验次数: 16/100
2025-08-08 23:08:31 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-08 23:08:32 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 23:08:32 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\KNN\optimization_history_20250808_230831.html
2025-08-08 23:08:32 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 23:08:32 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\KNN\param_importances_20250808_230832.html
2025-08-08 23:08:32 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.08 秒
2025-08-08 23:08:32 - hyperparameter_tuning - INFO - 开始对 NaiveBayes 进行超参数调优，试验次数: 100
2025-08-08 23:08:32 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 6
2025-08-08 23:08:32 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-08 23:08:32 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-08 23:08:32 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-08 23:08:32 - hyperparameter_tuning - INFO - NaiveBayes模型无需调参，计算基准得分
2025-08-08 23:08:32 - hyperparameter_tuning - INFO - NaiveBayes基准得分: 0.9573
2025-08-08 23:08:32 - hyperparameter_tuning - INFO - 开始对 NeuralNet 进行超参数调优，试验次数: 100
2025-08-08 23:08:32 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 6
2025-08-08 23:08:32 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-08 23:08:32 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-08 23:08:32 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-08 23:08:32 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 100, 'n_jobs': 6, 'timeout': 1800}
2025-08-08 23:08:38 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9864
2025-08-08 23:08:48 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:08:48 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9864
2025-08-08 23:08:48 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:08:48 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9864
2025-08-08 23:08:48 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:08:48 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9864
2025-08-08 23:08:49 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:08:49 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9864
2025-08-08 23:08:49 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:08:49 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9864
2025-08-08 23:08:49 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:08:49 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9864
2025-08-08 23:08:49 - hyperparameter_tuning - INFO - 模型 NeuralNet 的最佳参数: {'clf__hidden_layer_sizes': (50,), 'clf__alpha': 0.009952287486232241}
2025-08-08 23:08:49 - hyperparameter_tuning - INFO - 模型 NeuralNet 的最佳得分: 0.9864
2025-08-08 23:08:49 - hyperparameter_tuning - INFO - 实际执行试验次数: 16/100
2025-08-08 23:08:49 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-08 23:08:49 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 23:08:49 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\NeuralNet\optimization_history_20250808_230849.html
2025-08-08 23:08:49 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 23:08:49 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\NeuralNet\param_importances_20250808_230849.html
2025-08-08 23:08:49 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 17.15 秒
