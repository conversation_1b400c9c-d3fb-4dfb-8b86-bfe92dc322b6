#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型选择顾问工具
为多数据源集成学习提供智能模型选择建议
支持基于数据特征、模型性能和多样性的自动选择策略
"""

import numpy as np
import pandas as pd
from pathlib import Path
import json
from sklearn.model_selection import cross_val_score
from sklearn.metrics import accuracy_score, f1_score, roc_auc_score
from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import PCA
from sklearn.cluster import KMeans
import warnings
warnings.filterwarnings('ignore')

# 导入项目模块
import sys
sys.path.append(str(Path(__file__).parent.parent / 'code'))

from data_preprocessing import load_and_preprocess_data

class ModelSelectionAdvisor:
    """
    模型选择顾问类
    提供基于数据特征和性能的智能模型选择建议
    """
    
    def __init__(self):
        # 使用统一的模型函数配置
        from config import get_model_functions
        self.model_functions = get_model_functions()
        
        # 模型特性描述
        self.model_characteristics = {
            'DecisionTree': {'complexity': 'low', 'interpretability': 'high', 'overfitting_risk': 'high'},
            'RandomForest': {'complexity': 'medium', 'interpretability': 'medium', 'overfitting_risk': 'low'},
            'XGBoost': {'complexity': 'high', 'interpretability': 'low', 'overfitting_risk': 'medium'},
            'LightGBM': {'complexity': 'high', 'interpretability': 'low', 'overfitting_risk': 'medium'},
            'CatBoost': {'complexity': 'high', 'interpretability': 'low', 'overfitting_risk': 'low'},
            'Logistic': {'complexity': 'low', 'interpretability': 'high', 'overfitting_risk': 'low'},
            'SVM': {'complexity': 'medium', 'interpretability': 'low', 'overfitting_risk': 'medium'},
            'KNN': {'complexity': 'low', 'interpretability': 'medium', 'overfitting_risk': 'medium'},
            'NaiveBayes': {'complexity': 'low', 'interpretability': 'high', 'overfitting_risk': 'low'},
            'NeuralNet': {'complexity': 'high', 'interpretability': 'low', 'overfitting_risk': 'high'}
        }
    
    def analyze_data_characteristics(self, data_paths):
        """
        分析数据特征
        
        Args:
            data_paths: 数据文件路径列表
            
        Returns:
            dict: 数据特征分析结果
        """
        print("🔍 分析数据特征...")
        
        data_analysis = {}
        
        for i, data_path in enumerate(data_paths, 1):
            print(f"\n分析数据集 {i}: {data_path}")
            
            try:
                X_train, _, y_train, _ = load_and_preprocess_data(data_path)
                
                if X_train is None:
                    print(f"⚠️ 数据集 {i} 加载失败")
                    continue
                
                # 基本统计信息
                n_samples, n_features = X_train.shape
                n_classes = len(np.unique(y_train))
                class_balance = np.bincount(y_train) / len(y_train)
                
                # 数据复杂度分析
                scaler = StandardScaler()
                X_scaled = scaler.fit_transform(X_train)
                
                # PCA分析
                pca = PCA()
                pca.fit(X_scaled)
                explained_variance_ratio = pca.explained_variance_ratio_
                
                # 计算有效维度（累计方差贡献率达到95%的维度数）
                cumsum_variance = np.cumsum(explained_variance_ratio)
                effective_dims = np.argmax(cumsum_variance >= 0.95) + 1
                
                # 数据聚类分析
                try:
                    kmeans = KMeans(n_clusters=min(5, n_samples//10), random_state=42, n_init=10)
                    cluster_labels = kmeans.fit_predict(X_scaled)
                    cluster_separation = kmeans.inertia_
                except:
                    cluster_separation = None
                
                data_analysis[f'dataset_{i}'] = {
                    'path': data_path,
                    'n_samples': n_samples,
                    'n_features': n_features,
                    'n_classes': n_classes,
                    'class_balance': class_balance.tolist(),
                    'effective_dims': effective_dims,
                    'dimensionality_ratio': effective_dims / n_features,
                    'cluster_separation': cluster_separation,
                    'complexity_score': self._calculate_complexity_score(
                        n_samples, n_features, effective_dims, class_balance
                    )
                }
                
                print(f"  样本数: {n_samples}, 特征数: {n_features}")
                print(f"  类别数: {n_classes}, 有效维度: {effective_dims}")
                print(f"  复杂度评分: {data_analysis[f'dataset_{i}']['complexity_score']:.2f}")
                
            except Exception as e:
                print(f"❌ 分析数据集 {i} 时出错: {e}")
        
        return data_analysis
    
    def _calculate_complexity_score(self, n_samples, n_features, effective_dims, class_balance):
        """
        计算数据复杂度评分
        
        Args:
            n_samples: 样本数
            n_features: 特征数
            effective_dims: 有效维度
            class_balance: 类别平衡度
            
        Returns:
            float: 复杂度评分 (0-1，越高越复杂)
        """
        # 维度复杂度
        dim_complexity = min(effective_dims / 100, 1.0)
        
        # 样本稀疏度
        sparsity = min(n_features / n_samples, 1.0)
        
        # 类别不平衡度
        imbalance = 1 - min(class_balance)
        
        # 综合复杂度
        complexity = (dim_complexity * 0.4 + sparsity * 0.3 + imbalance * 0.3)
        
        return complexity
    
    def evaluate_model_performance(self, data_paths, selected_models=None, cv_folds=3):
        """
        评估模型在各数据集上的性能
        
        Args:
            data_paths: 数据文件路径列表
            selected_models: 选择的模型列表，None表示使用所有模型
            cv_folds: 交叉验证折数
            
        Returns:
            dict: 模型性能评估结果
        """
        print("\n📊 评估模型性能...")
        
        if selected_models is None:
            selected_models = list(self.model_functions.keys())
        
        performance_results = {}
        
        for i, data_path in enumerate(data_paths, 1):
            print(f"\n评估数据集 {i}: {data_path}")
            
            try:
                X_train, X_test, y_train, y_test = load_and_preprocess_data(data_path)
                
                if X_train is None:
                    continue
                
                dataset_results = {}
                
                for model_name in selected_models:
                    print(f"  测试模型: {model_name}")
                    
                    try:
                        # 训练模型
                        train_func = self.model_functions[model_name]
                        model = train_func(X_train, y_train, X_test, y_test)
                        
                        # 交叉验证评估
                        cv_scores = cross_val_score(
                            model, X_train, y_train, 
                            cv=cv_folds, scoring='roc_auc'
                        )
                        
                        # 测试集评估
                        y_pred = model.predict(X_test)
                        test_accuracy = accuracy_score(y_test, y_pred)
                        test_f1 = f1_score(y_test, y_pred, average='weighted')
                        
                        if hasattr(model, 'predict_proba'):
                            y_pred_proba = model.predict_proba(X_test)[:, 1]
                            test_auc = roc_auc_score(y_test, y_pred_proba)
                        else:
                            test_auc = 0.5
                        
                        dataset_results[model_name] = {
                            'cv_mean': np.mean(cv_scores),
                            'cv_std': np.std(cv_scores),
                            'test_accuracy': test_accuracy,
                            'test_f1': test_f1,
                            'test_auc': test_auc,
                            'overall_score': (np.mean(cv_scores) + test_auc) / 2
                        }
                        
                        print(f"    CV AUC: {np.mean(cv_scores):.3f}±{np.std(cv_scores):.3f}")
                        print(f"    测试 AUC: {test_auc:.3f}")
                        
                    except Exception as e:
                        print(f"    ❌ 模型 {model_name} 评估失败: {e}")
                        dataset_results[model_name] = {
                            'cv_mean': 0, 'cv_std': 0, 'test_accuracy': 0,
                            'test_f1': 0, 'test_auc': 0, 'overall_score': 0
                        }
                
                performance_results[f'dataset_{i}'] = dataset_results
                
            except Exception as e:
                print(f"❌ 评估数据集 {i} 时出错: {e}")
        
        return performance_results
    
    def calculate_model_diversity(self, performance_results):
        """
        计算模型多样性
        
        Args:
            performance_results: 模型性能评估结果
            
        Returns:
            dict: 模型多样性分析结果
        """
        print("\n🎯 分析模型多样性...")
        
        all_models = set()
        for dataset_results in performance_results.values():
            all_models.update(dataset_results.keys())
        
        all_models = list(all_models)
        
        # 构建性能矩阵
        performance_matrix = []
        for model in all_models:
            model_scores = []
            for dataset_results in performance_results.values():
                score = dataset_results.get(model, {}).get('overall_score', 0)
                model_scores.append(score)
            performance_matrix.append(model_scores)
        
        performance_matrix = np.array(performance_matrix)
        
        # 计算模型间相关性
        correlation_matrix = np.corrcoef(performance_matrix)
        
        # 计算多样性分数（基于相关性的负值）
        diversity_scores = {}
        for i, model in enumerate(all_models):
            # 与其他模型的平均相关性
            avg_correlation = np.mean([correlation_matrix[i, j] 
                                     for j in range(len(all_models)) if i != j])
            diversity_scores[model] = 1 - avg_correlation
        
        return diversity_scores
    
    def recommend_optimal_models(self, data_paths, target_ensemble_size=3, 
                               selection_strategy='balanced'):
        """
        推荐最优模型组合
        
        Args:
            data_paths: 数据文件路径列表
            target_ensemble_size: 目标集成模型数量
            selection_strategy: 选择策略 ('performance', 'diversity', 'balanced')
            
        Returns:
            dict: 推荐结果
        """
        print("\n🚀 开始模型选择分析...")
        
        # 1. 分析数据特征
        data_analysis = self.analyze_data_characteristics(data_paths)
        
        # 2. 评估模型性能
        performance_results = self.evaluate_model_performance(data_paths)
        
        # 3. 计算模型多样性
        diversity_scores = self.calculate_model_diversity(performance_results)
        
        # 4. 综合评分和推荐
        recommendations = self._generate_recommendations(
            data_analysis, performance_results, diversity_scores,
            target_ensemble_size, selection_strategy
        )
        
        return {
            'data_analysis': data_analysis,
            'performance_results': performance_results,
            'diversity_scores': diversity_scores,
            'recommendations': recommendations
        }
    
    def _generate_recommendations(self, data_analysis, performance_results, 
                                diversity_scores, target_size, strategy):
        """
        生成模型推荐
        
        Args:
            data_analysis: 数据分析结果
            performance_results: 性能评估结果
            diversity_scores: 多样性分数
            target_size: 目标模型数量
            strategy: 选择策略
            
        Returns:
            dict: 推荐结果
        """
        print("\n🎯 生成模型推荐...")
        
        # 计算每个模型的综合评分
        model_scores = {}
        
        for model in diversity_scores.keys():
            # 性能评分（所有数据集的平均性能）
            performance_score = 0
            count = 0
            for dataset_results in performance_results.values():
                if model in dataset_results:
                    performance_score += dataset_results[model]['overall_score']
                    count += 1
            
            if count > 0:
                performance_score /= count
            
            # 多样性评分
            diversity_score = diversity_scores[model]
            
            # 根据策略计算综合评分
            if strategy == 'performance':
                final_score = performance_score
            elif strategy == 'diversity':
                final_score = diversity_score
            else:  # balanced
                final_score = 0.7 * performance_score + 0.3 * diversity_score
            
            model_scores[model] = {
                'performance_score': performance_score,
                'diversity_score': diversity_score,
                'final_score': final_score
            }
        
        # 选择top模型
        sorted_models = sorted(model_scores.items(), 
                             key=lambda x: x[1]['final_score'], reverse=True)
        
        recommended_models = [model for model, _ in sorted_models[:target_size]]
        
        # 生成数据源映射建议
        data_mapping_suggestions = self._suggest_data_mapping(
            recommended_models, data_analysis, performance_results
        )
        
        return {
            'recommended_models': recommended_models,
            'model_scores': model_scores,
            'data_mapping_suggestions': data_mapping_suggestions,
            'selection_strategy': strategy,
            'reasoning': self._generate_reasoning(
                recommended_models, model_scores, data_analysis
            )
        }
    
    def _suggest_data_mapping(self, recommended_models, data_analysis, performance_results):
        """
        建议模型与数据源的映射
        
        Args:
            recommended_models: 推荐的模型列表
            data_analysis: 数据分析结果
            performance_results: 性能评估结果
            
        Returns:
            dict: 数据映射建议
        """
        mapping_suggestions = {}
        
        for model in recommended_models:
            best_dataset = None
            best_score = 0
            
            for dataset_key, dataset_results in performance_results.items():
                if model in dataset_results:
                    score = dataset_results[model]['overall_score']
                    if score > best_score:
                        best_score = score
                        best_dataset = dataset_key
            
            if best_dataset:
                dataset_info = data_analysis[best_dataset]
                mapping_suggestions[model] = {
                    'recommended_dataset': best_dataset,
                    'dataset_path': dataset_info['path'],
                    'performance_score': best_score,
                    'dataset_complexity': dataset_info['complexity_score']
                }
        
        return mapping_suggestions
    
    def _generate_reasoning(self, recommended_models, model_scores, data_analysis):
        """
        生成推荐理由
        
        Args:
            recommended_models: 推荐的模型列表
            model_scores: 模型评分
            data_analysis: 数据分析结果
            
        Returns:
            str: 推荐理由
        """
        reasoning = []
        
        reasoning.append(f"基于性能和多样性分析，推荐以下 {len(recommended_models)} 个模型进行集成：")
        
        for i, model in enumerate(recommended_models, 1):
            scores = model_scores[model]
            characteristics = self.model_characteristics[model]
            
            reasoning.append(
                f"{i}. {model}: "
                f"性能评分 {scores['performance_score']:.3f}, "
                f"多样性评分 {scores['diversity_score']:.3f}, "
                f"综合评分 {scores['final_score']:.3f}"
            )
            
            reasoning.append(
                f"   特点: 复杂度{characteristics['complexity']}, "
                f"可解释性{characteristics['interpretability']}, "
                f"过拟合风险{characteristics['overfitting_risk']}"
            )
        
        # 数据集特征总结
        avg_complexity = np.mean([info['complexity_score'] 
                                for info in data_analysis.values()])
        
        if avg_complexity > 0.7:
            reasoning.append("\n数据集复杂度较高，建议使用集成方法以提高泛化能力。")
        elif avg_complexity < 0.3:
            reasoning.append("\n数据集相对简单，可以考虑使用较简单的集成策略。")
        else:
            reasoning.append("\n数据集复杂度适中，推荐使用平衡的集成策略。")
        
        return "\n".join(reasoning)
    
    def save_recommendations(self, results, output_path):
        """
        保存推荐结果
        
        Args:
            results: 推荐结果
            output_path: 输出路径
        """
        output_path = Path(output_path)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # 保存JSON格式的详细结果
        with open(output_path / 'model_selection_results.json', 'w', encoding='utf-8') as f:
            # 转换numpy数组为列表以便JSON序列化
            json_results = self._convert_for_json(results)
            json.dump(json_results, f, indent=2, ensure_ascii=False)
        
        # 生成可读的推荐报告
        self._generate_readable_report(results, output_path)
        
        print(f"\n📁 推荐结果已保存至: {output_path}")
    
    def _convert_for_json(self, obj):
        """
        转换对象为JSON可序列化格式
        """
        if isinstance(obj, dict):
            return {k: self._convert_for_json(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._convert_for_json(item) for item in obj]
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, (np.integer, np.floating)):
            return float(obj)
        else:
            return obj
    
    def _generate_readable_report(self, results, output_path):
        """
        生成可读的推荐报告
        """
        recommendations = results['recommendations']
        
        report_lines = [
            "# 模型选择推荐报告",
            "",
            "## 📋 推荐模型",
            ""
        ]
        
        for i, model in enumerate(recommendations['recommended_models'], 1):
            scores = recommendations['model_scores'][model]
            report_lines.extend([
                f"### {i}. {model}",
                f"- 性能评分: {scores['performance_score']:.3f}",
                f"- 多样性评分: {scores['diversity_score']:.3f}",
                f"- 综合评分: {scores['final_score']:.3f}",
                ""
            ])
        
        report_lines.extend([
            "## 🎯 推荐理由",
            "",
            recommendations['reasoning'],
            "",
            "## 📊 数据映射建议",
            ""
        ])
        
        for model, mapping in recommendations['data_mapping_suggestions'].items():
            report_lines.extend([
                f"### {model}",
                f"- 推荐数据集: {mapping['recommended_dataset']}",
                f"- 数据路径: {mapping['dataset_path']}",
                f"- 性能评分: {mapping['performance_score']:.3f}",
                ""
            ])
        
        # 保存报告
        with open(output_path / 'model_selection_report.md', 'w', encoding='utf-8') as f:
            f.write("\n".join(report_lines))

def main():
    """
    主函数 - 命令行接口
    """
    import argparse
    
    parser = argparse.ArgumentParser(description='模型选择顾问工具')
    parser.add_argument('--data-paths', nargs='+', required=True,
                       help='数据文件路径列表')
    parser.add_argument('--ensemble-size', type=int, default=3,
                       help='目标集成模型数量 (默认: 3)')
    parser.add_argument('--strategy', choices=['performance', 'diversity', 'balanced'],
                       default='balanced', help='选择策略 (默认: balanced)')
    parser.add_argument('--output', default='./model_selection_results',
                       help='输出目录 (默认: ./model_selection_results)')
    
    args = parser.parse_args()
    
    # 创建顾问实例
    advisor = ModelSelectionAdvisor()
    
    # 执行推荐
    results = advisor.recommend_optimal_models(
        data_paths=args.data_paths,
        target_ensemble_size=args.ensemble_size,
        selection_strategy=args.strategy
    )
    
    # 保存结果
    advisor.save_recommendations(results, args.output)
    
    # 打印推荐结果
    print("\n" + "="*60)
    print("🎯 模型选择推荐结果")
    print("="*60)
    
    recommendations = results['recommendations']
    print(f"\n推荐的 {len(recommendations['recommended_models'])} 个模型:")
    for i, model in enumerate(recommendations['recommended_models'], 1):
        scores = recommendations['model_scores'][model]
        print(f"{i}. {model} (综合评分: {scores['final_score']:.3f})")
    
    print(f"\n推荐理由:\n{recommendations['reasoning']}")
    
    print(f"\n详细结果已保存至: {args.output}")

if __name__ == "__main__":
    main()