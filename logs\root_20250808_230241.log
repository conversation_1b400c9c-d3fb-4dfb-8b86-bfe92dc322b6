2025-08-08 23:02:41 - hyperparameter_tuning - INFO - 开始对 CatBoost 进行超参数调优，试验次数: 8
2025-08-08 23:02:41 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-08 23:02:41 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-08 23:02:41 - hyperparameter_tuning - INFO - CatBoost使用串行模式避免GPU设备冲突
2025-08-08 23:02:41 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 8, 'n_jobs': 1}
2025-08-08 23:03:06 - hyperparameter_tuning - INFO - 模型 CatBoost 的最佳参数: {'iterations': 247, 'depth': 3, 'learning_rate': 0.15912798713994736, 'l2_leaf_reg': 6.331731119758382, 'bagging_temperature': 0.046450412719997725}
2025-08-08 23:03:06 - hyperparameter_tuning - INFO - 模型 CatBoost 的最佳得分: 0.9469
2025-08-08 23:03:06 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-08 23:03:06 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 23:03:06 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\CatBoost\optimization_history_20250808_230306.html
2025-08-08 23:03:06 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-08 23:03:06 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 23:03:06 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\CatBoost\param_importances_20250808_230306.html
2025-08-08 23:03:06 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 25.10 秒
2025-08-08 23:03:06 - hyperparameter_tuning - INFO - 开始对 RandomForest 进行超参数调优，试验次数: 8
2025-08-08 23:03:06 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-08 23:03:06 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-08 23:03:06 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 8, 'n_jobs': 4}
2025-08-08 23:03:14 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳参数: {'n_estimators': 115, 'max_depth': 24, 'min_samples_split': 7, 'min_samples_leaf': 11, 'max_features': 'log2'}
2025-08-08 23:03:14 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳得分: 0.9258
2025-08-08 23:03:14 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-08 23:03:14 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 23:03:14 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\optimization_history_20250808_230314.html
2025-08-08 23:03:14 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-08 23:03:14 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 23:03:14 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\param_importances_20250808_230314.html
2025-08-08 23:03:14 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 8.07 秒
