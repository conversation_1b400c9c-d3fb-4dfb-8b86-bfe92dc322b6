2025-08-08 22:49:03 - hyperparameter_tuning - INFO - 开始对 DecisionTree 进行超参数调优，试验次数: 100
2025-08-08 22:49:03 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-08 22:49:03 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-08 22:49:03 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-08 22:49:03 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-08 22:49:03 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 100, 'n_jobs': 8, 'timeout': 1800}
2025-08-08 22:49:03 - hyperparameter_tuning - INFO - Trial 7: 发现更好的得分 0.9374
2025-08-08 22:49:03 - hyperparameter_tuning - INFO - Trial 6: 发现更好的得分 0.9438
2025-08-08 22:49:03 - hyperparameter_tuning - INFO - Trial 5: 发现更好的得分 0.9490
2025-08-08 22:49:03 - hyperparameter_tuning - INFO - Trial 12: 发现更好的得分 0.9541
2025-08-08 22:49:03 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 22:49:03 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9541
2025-08-08 22:49:04 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 22:49:04 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9541
2025-08-08 22:49:04 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 22:49:04 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9541
2025-08-08 22:49:04 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 22:49:04 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 22:49:04 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9541
2025-08-08 22:49:04 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9541
2025-08-08 22:49:04 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 22:49:04 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9541
2025-08-08 22:49:04 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 22:49:04 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9541
2025-08-08 22:49:04 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 22:49:04 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9541
2025-08-08 22:49:04 - hyperparameter_tuning - INFO - 模型 DecisionTree 的最佳参数: {'max_depth': 8, 'min_samples_split': 18, 'min_samples_leaf': 13, 'criterion': 'entropy', 'class_weight': 'balanced', 'max_features': None}
2025-08-08 22:49:04 - hyperparameter_tuning - INFO - 模型 DecisionTree 的最佳得分: 0.9541
2025-08-08 22:49:04 - hyperparameter_tuning - INFO - 实际执行试验次数: 31/100
2025-08-08 22:49:04 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-08 22:49:04 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 22:49:04 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\DecisionTree\optimization_history_20250808_224904.html
2025-08-08 22:49:04 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 22:49:04 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\DecisionTree\param_importances_20250808_224904.html
2025-08-08 22:49:04 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.45 秒
2025-08-08 22:49:04 - hyperparameter_tuning - INFO - 开始对 RandomForest 进行超参数调优，试验次数: 100
2025-08-08 22:49:04 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-08 22:49:04 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-08 22:49:04 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-08 22:49:04 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-08 22:49:04 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 100, 'n_jobs': 8, 'timeout': 1800}
2025-08-08 22:49:06 - hyperparameter_tuning - INFO - Trial 3: 发现更好的得分 0.9835
2025-08-08 22:49:13 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 22:49:13 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9835
2025-08-08 22:49:15 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 22:49:15 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9835
2025-08-08 22:49:15 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 22:49:15 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9835
2025-08-08 22:49:15 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 22:49:15 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9835
2025-08-08 22:49:16 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 22:49:16 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9835
2025-08-08 22:49:17 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 22:49:17 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9835
2025-08-08 22:49:17 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 22:49:17 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9835
2025-08-08 22:49:17 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 22:49:17 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9836
2025-08-08 22:49:17 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳参数: {'n_estimators': 290, 'max_depth': 2, 'min_samples_split': 2, 'min_samples_leaf': 2, 'max_features': 'sqrt'}
2025-08-08 22:49:17 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳得分: 0.9836
2025-08-08 22:49:17 - hyperparameter_tuning - INFO - 实际执行试验次数: 18/100
2025-08-08 22:49:17 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-08 22:49:18 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 22:49:18 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\optimization_history_20250808_224917.html
2025-08-08 22:49:18 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 22:49:18 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\param_importances_20250808_224918.html
2025-08-08 22:49:18 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 13.73 秒
2025-08-08 22:49:18 - hyperparameter_tuning - INFO - 开始对 XGBoost 进行超参数调优，试验次数: 100
2025-08-08 22:49:18 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-08 22:49:18 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-08 22:49:18 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-08 22:49:18 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-08 22:49:18 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 100, 'n_jobs': 8, 'timeout': 1800}
2025-08-08 22:49:18 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 22:49:18 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 22:49:18 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 22:49:18 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 22:49:18 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 22:49:18 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 22:49:18 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 22:49:18 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 22:49:19 - hyperparameter_tuning - INFO - Trial 2: 发现更好的得分 0.9860
2025-08-08 22:49:19 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 22:49:19 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 22:49:19 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 22:49:19 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 22:49:20 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 22:49:20 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 22:49:20 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 22:49:20 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 22:49:20 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9876
2025-08-08 22:49:20 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 22:49:20 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 22:49:20 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 22:49:20 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 22:49:20 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 22:49:21 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 22:49:21 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 22:49:21 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 22:49:21 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 22:49:22 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 22:49:22 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 22:49:22 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9876
2025-08-08 22:49:22 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 22:49:22 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9876
2025-08-08 22:49:22 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 22:49:22 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9876
2025-08-08 22:49:23 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 22:49:23 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9884
2025-08-08 22:49:23 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 22:49:23 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9884
2025-08-08 22:49:23 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 22:49:23 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9884
2025-08-08 22:49:23 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 22:49:23 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9884
2025-08-08 22:49:23 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 22:49:23 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9884
2025-08-08 22:49:23 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳参数: {'n_estimators': 300, 'max_depth': 7, 'learning_rate': 0.20799594059054105, 'subsample': 0.9982014377925961, 'colsample_bytree': 0.8028581188723667}
2025-08-08 22:49:23 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳得分: 0.9884
2025-08-08 22:49:23 - hyperparameter_tuning - INFO - 实际执行试验次数: 26/100
2025-08-08 22:49:23 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-08 22:49:23 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 22:49:23 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\XGBoost\optimization_history_20250808_224923.html
2025-08-08 22:49:24 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 22:49:24 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\XGBoost\param_importances_20250808_224923.html
2025-08-08 22:49:24 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 5.74 秒
2025-08-08 22:49:24 - hyperparameter_tuning - INFO - 开始对 LightGBM 进行超参数调优，试验次数: 100
2025-08-08 22:49:24 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-08 22:49:24 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-08 22:49:24 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-08 22:49:24 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-08 22:49:24 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 100, 'n_jobs': 8, 'timeout': 1800}
2025-08-08 22:49:31 - hyperparameter_tuning - INFO - Trial 6: 发现更好的得分 0.9809
2025-08-08 22:49:31 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9857
2025-08-08 22:49:31 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9876
2025-08-08 22:49:31 - hyperparameter_tuning - INFO - Trial 9: 发现更好的得分 0.9891
2025-08-08 22:49:32 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 22:49:32 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9891
2025-08-08 22:49:32 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 22:49:32 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9891
2025-08-08 22:49:33 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 22:49:33 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9891
2025-08-08 22:49:33 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 22:49:33 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9891
2025-08-08 22:49:33 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 22:49:33 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9891
2025-08-08 22:49:33 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 22:49:33 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9891
2025-08-08 22:49:33 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 22:49:33 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9891
2025-08-08 22:49:33 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 22:49:33 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9891
2025-08-08 22:49:33 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳参数: {'n_estimators': 76, 'max_depth': 5, 'learning_rate': 0.11365832945911504, 'feature_fraction': 0.7422499170151668, 'bagging_fraction': 0.7640727393089688}
2025-08-08 22:49:33 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳得分: 0.9891
2025-08-08 22:49:33 - hyperparameter_tuning - INFO - 实际执行试验次数: 24/100
2025-08-08 22:49:33 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-08 22:49:33 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 22:49:33 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\LightGBM\optimization_history_20250808_224933.html
2025-08-08 22:49:33 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 22:49:33 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\LightGBM\param_importances_20250808_224933.html
2025-08-08 22:49:33 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 9.59 秒
2025-08-08 22:49:33 - hyperparameter_tuning - INFO - 开始对 CatBoost 进行超参数调优，试验次数: 100
2025-08-08 22:49:33 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-08 22:49:33 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-08 22:49:33 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-08 22:49:33 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-08 22:49:33 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 100, 'n_jobs': 8, 'timeout': 1800}
2025-08-08 22:49:33 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-08 22:49:33 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-08 22:49:33 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-08 22:49:33 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-08 22:49:33 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-08 22:49:33 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-08 22:49:33 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-08 22:49:33 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-08 22:49:37 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9769
2025-08-08 22:49:37 - hyperparameter_tuning - ERROR - 超参数调优过程中出错: 
All the 5 fits failed.
It is very likely that your model is misconfigured.
You can try to debug the error by setting error_score='raise'.

Below are more details about the failures:
--------------------------------------------------------------------------------
1 <USER> <GROUP> with the following error:
Traceback (most recent call last):
  File "D:\anaconda\envs\multi_model\lib\site-packages\sklearn\model_selection\_validation.py", line 866, in _fit_and_score
    estimator.fit(X_train, y_train, **fit_params)
  File "D:\anaconda\envs\multi_model\lib\site-packages\catboost\core.py", line 5245, in fit
    self._fit(X, y, cat_features, text_features, embedding_features, None, graph, sample_weight, None, None, None, None, baseline, use_best_model,
  File "D:\anaconda\envs\multi_model\lib\site-packages\catboost\core.py", line 2410, in _fit
    self._train(
  File "D:\anaconda\envs\multi_model\lib\site-packages\catboost\core.py", line 1790, in _train
    self._object._train(train_pool, test_pool, params, allow_clear_pool, init_model._object if init_model else None)
  File "_catboost.pyx", line 5017, in _catboost._CatBoost._train
  File "_catboost.pyx", line 5066, in _catboost._CatBoost._train
_catboost.CatBoostError: catboost/cuda/cuda_lib/devices_provider.h:190: Error: device already requested 0

--------------------------------------------------------------------------------
4 <USER> <GROUP> with the following error:
Traceback (most recent call last):
  File "D:\anaconda\envs\multi_model\lib\site-packages\sklearn\model_selection\_validation.py", line 866, in _fit_and_score
    estimator.fit(X_train, y_train, **fit_params)
  File "D:\anaconda\envs\multi_model\lib\site-packages\catboost\core.py", line 5245, in fit
    self._fit(X, y, cat_features, text_features, embedding_features, None, graph, sample_weight, None, None, None, None, baseline, use_best_model,
  File "D:\anaconda\envs\multi_model\lib\site-packages\catboost\core.py", line 2410, in _fit
    self._train(
  File "D:\anaconda\envs\multi_model\lib\site-packages\catboost\core.py", line 1790, in _train
    self._object._train(train_pool, test_pool, params, allow_clear_pool, init_model._object if init_model else None)
  File "_catboost.pyx", line 5017, in _catboost._CatBoost._train
  File "_catboost.pyx", line 5066, in _catboost._CatBoost._train
_catboost.CatBoostError: catboost/cuda/cuda_lib/cuda_manager.cpp:201: Condition violated: `State == nullptr'

2025-08-08 22:49:37 - hyperparameter_tuning - INFO - 开始对 Logistic 进行超参数调优，试验次数: 100
2025-08-08 22:49:37 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-08 22:49:37 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-08 22:49:37 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-08 22:49:37 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-08 22:49:37 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 100, 'n_jobs': 8, 'timeout': 1800}
2025-08-08 22:49:37 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9731
2025-08-08 22:49:37 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 22:49:37 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9740
2025-08-08 22:49:37 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 22:49:37 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9740
2025-08-08 22:49:37 - hyperparameter_tuning - INFO - Trial 16: 发现更好的得分 0.9747
2025-08-08 22:49:37 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳参数: {'clf__C': 9.275418020588079, 'clf__solver': 'liblinear'}
2025-08-08 22:49:37 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳得分: 0.9747
2025-08-08 22:49:37 - hyperparameter_tuning - INFO - 实际执行试验次数: 18/100
2025-08-08 22:49:37 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-08 22:49:37 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 22:49:37 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\Logistic\optimization_history_20250808_224937.html
2025-08-08 22:49:38 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 22:49:38 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\Logistic\param_importances_20250808_224937.html
2025-08-08 22:49:38 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.13 秒
2025-08-08 22:49:38 - hyperparameter_tuning - INFO - 开始对 SVM 进行超参数调优，试验次数: 100
2025-08-08 22:49:38 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-08 22:49:38 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-08 22:49:38 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-08 22:49:38 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-08 22:49:38 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 100, 'n_jobs': 8, 'timeout': 1800}
2025-08-08 22:49:38 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9731
2025-08-08 22:49:38 - hyperparameter_tuning - INFO - Trial 4: 发现更好的得分 0.9876
2025-08-08 22:49:38 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 22:49:38 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9876
2025-08-08 22:49:38 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 22:49:38 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 22:49:38 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 22:49:38 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9876
2025-08-08 22:49:38 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9876
2025-08-08 22:49:38 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9876
2025-08-08 22:49:38 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 22:49:38 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9876
2025-08-08 22:49:38 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 22:49:38 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9876
2025-08-08 22:49:38 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 22:49:38 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 22:49:38 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9884
2025-08-08 22:49:38 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9884
2025-08-08 22:49:38 - hyperparameter_tuning - INFO - 模型 SVM 的最佳参数: {'clf__C': 5.950009965267607, 'clf__kernel': 'rbf'}
2025-08-08 22:49:38 - hyperparameter_tuning - INFO - 模型 SVM 的最佳得分: 0.9884
2025-08-08 22:49:38 - hyperparameter_tuning - INFO - 实际执行试验次数: 20/100
2025-08-08 22:49:38 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-08 22:49:39 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 22:49:39 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\SVM\optimization_history_20250808_224938.html
2025-08-08 22:49:39 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 22:49:39 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\SVM\param_importances_20250808_224939.html
2025-08-08 22:49:39 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.31 秒
2025-08-08 22:49:39 - hyperparameter_tuning - INFO - 开始对 KNN 进行超参数调优，试验次数: 100
2025-08-08 22:49:39 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-08 22:49:39 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-08 22:49:39 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-08 22:49:39 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-08 22:49:39 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 100, 'n_jobs': 8, 'timeout': 1800}
2025-08-08 22:49:39 - hyperparameter_tuning - INFO - Trial 4: 发现更好的得分 0.9812
2025-08-08 22:49:39 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 22:49:39 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9816
2025-08-08 22:49:39 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 22:49:40 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9816
2025-08-08 22:49:40 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 22:49:40 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9816
2025-08-08 22:49:40 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 22:49:40 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9816
2025-08-08 22:49:40 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 22:49:40 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9816
2025-08-08 22:49:40 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 22:49:40 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9816
2025-08-08 22:49:40 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 22:49:40 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9816
2025-08-08 22:49:40 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 22:49:40 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9816
2025-08-08 22:49:40 - hyperparameter_tuning - INFO - 模型 KNN 的最佳参数: {'clf__n_neighbors': 4}
2025-08-08 22:49:40 - hyperparameter_tuning - INFO - 模型 KNN 的最佳得分: 0.9816
2025-08-08 22:49:40 - hyperparameter_tuning - INFO - 实际执行试验次数: 18/100
2025-08-08 22:49:40 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-08 22:49:40 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 22:49:40 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\KNN\optimization_history_20250808_224940.html
2025-08-08 22:49:40 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 22:49:40 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\KNN\param_importances_20250808_224940.html
2025-08-08 22:49:40 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.11 秒
2025-08-08 22:49:40 - hyperparameter_tuning - INFO - 开始对 NaiveBayes 进行超参数调优，试验次数: 100
2025-08-08 22:49:40 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-08 22:49:40 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-08 22:49:40 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-08 22:49:40 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-08 22:49:40 - hyperparameter_tuning - INFO - NaiveBayes模型无需调参
2025-08-08 22:49:40 - hyperparameter_tuning - INFO - 开始对 NeuralNet 进行超参数调优，试验次数: 100
2025-08-08 22:49:40 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-08 22:49:40 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-08 22:49:40 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-08 22:49:40 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-08 22:49:40 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 100, 'n_jobs': 8, 'timeout': 1800}
2025-08-08 22:49:46 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9857
2025-08-08 22:49:53 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 22:49:53 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9857
2025-08-08 22:49:56 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 22:49:56 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9857
2025-08-08 22:49:58 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 22:49:58 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9857
2025-08-08 22:50:00 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 22:50:00 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9857
2025-08-08 22:50:00 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 22:50:00 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9857
2025-08-08 22:50:00 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 22:50:00 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9857
2025-08-08 22:50:01 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 22:50:01 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9857
2025-08-08 22:50:01 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 22:50:01 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9857
2025-08-08 22:50:01 - hyperparameter_tuning - INFO - 模型 NeuralNet 的最佳参数: {'clf__hidden_layer_sizes': (50,), 'clf__alpha': 0.007884063171442456}
2025-08-08 22:50:01 - hyperparameter_tuning - INFO - 模型 NeuralNet 的最佳得分: 0.9857
2025-08-08 22:50:01 - hyperparameter_tuning - INFO - 实际执行试验次数: 18/100
2025-08-08 22:50:01 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-08 22:50:01 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 22:50:01 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\NeuralNet\optimization_history_20250808_225001.html
2025-08-08 22:50:02 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 22:50:02 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\NeuralNet\param_importances_20250808_225001.html
2025-08-08 22:50:02 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 21.41 秒
