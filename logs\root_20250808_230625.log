2025-08-08 23:06:26 - training_session_manager - INFO - 初始化训练会话管理器
2025-08-08 23:06:27 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-08 23:06:27 - GUI - INFO - 字体管理器初始化成功，使用英文字体
2025-08-08 23:06:28 - data_exploration - INFO - 数据探索器初始化完成，输出目录: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\data_exploration
2025-08-08 23:06:28 - gui_data_exploration - INFO - 数据探索GUI组件初始化完成
2025-08-08 23:06:28 - GUI - INFO - GUI界面初始化完成
2025-08-08 23:06:58 - data_preprocessing - INFO - 成功加载 数据，特征维度: (197, 8)
2025-08-08 23:06:58 - data_preprocessing - INFO - 应用了 standard 特征缩放
2025-08-08 23:06:58 - hyperparameter_tuning - INFO - 开始对 DecisionTree 进行超参数调优，试验次数: 100
2025-08-08 23:06:58 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 6
2025-08-08 23:06:58 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-08 23:06:58 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-08 23:06:58 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-08 23:06:58 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 100, 'n_jobs': 6, 'timeout': 1800}
2025-08-08 23:06:58 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9125
2025-08-08 23:06:58 - hyperparameter_tuning - INFO - Trial 4: 发现更好的得分 0.9279
2025-08-08 23:06:58 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9411
2025-08-08 23:06:58 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:06:58 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:06:58 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9411
2025-08-08 23:06:58 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9411
2025-08-08 23:06:58 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:06:58 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9411
2025-08-08 23:06:58 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:06:58 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9411
2025-08-08 23:06:58 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:06:58 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:06:58 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9411
2025-08-08 23:06:58 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9411
2025-08-08 23:06:58 - hyperparameter_tuning - INFO - 模型 DecisionTree 的最佳参数: {'max_depth': 4, 'min_samples_split': 23, 'min_samples_leaf': 19, 'criterion': 'gini', 'class_weight': 'balanced', 'max_features': None}
2025-08-08 23:06:58 - hyperparameter_tuning - INFO - 模型 DecisionTree 的最佳得分: 0.9411
2025-08-08 23:06:58 - hyperparameter_tuning - INFO - 实际执行试验次数: 18/100
2025-08-08 23:06:58 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-08 23:06:58 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-08 23:06:59 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 23:06:59 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\DecisionTree\optimization_history_20250808_230658.html
2025-08-08 23:06:59 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-08 23:06:59 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 23:06:59 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\DecisionTree\param_importances_20250808_230659.html
2025-08-08 23:06:59 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.06 秒
2025-08-08 23:06:59 - hyperparameter_tuning - INFO - 开始对 RandomForest 进行超参数调优，试验次数: 100
2025-08-08 23:06:59 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 6
2025-08-08 23:06:59 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-08 23:06:59 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-08 23:06:59 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-08 23:06:59 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 100, 'n_jobs': 6, 'timeout': 1800}
2025-08-08 23:07:01 - hyperparameter_tuning - INFO - Trial 3: 发现更好的得分 0.9827
2025-08-08 23:07:08 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:07:08 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9836
2025-08-08 23:07:08 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:07:08 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9836
2025-08-08 23:07:09 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:07:09 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9836
2025-08-08 23:07:09 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:07:09 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9836
2025-08-08 23:07:09 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:07:09 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9836
2025-08-08 23:07:10 - hyperparameter_tuning - INFO - Trial 15: 发现更好的得分 0.9853
2025-08-08 23:07:10 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳参数: {'n_estimators': 295, 'max_depth': 2, 'min_samples_split': 2, 'min_samples_leaf': 1, 'max_features': 'sqrt'}
2025-08-08 23:07:10 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳得分: 0.9853
2025-08-08 23:07:10 - hyperparameter_tuning - INFO - 实际执行试验次数: 16/100
2025-08-08 23:07:10 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-08 23:07:10 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-08 23:07:10 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 23:07:10 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\optimization_history_20250808_230710.html
2025-08-08 23:07:10 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-08 23:07:11 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 23:07:11 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\param_importances_20250808_230710.html
2025-08-08 23:07:11 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 11.67 秒
2025-08-08 23:07:11 - hyperparameter_tuning - INFO - 开始对 XGBoost 进行超参数调优，试验次数: 100
2025-08-08 23:07:11 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 6
2025-08-08 23:07:11 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-08 23:07:11 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-08 23:07:11 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-08 23:07:11 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 100, 'n_jobs': 6, 'timeout': 1800}
2025-08-08 23:07:11 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 23:07:11 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 23:07:11 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 23:07:11 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 23:07:11 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 23:07:11 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 23:07:11 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9851
2025-08-08 23:07:11 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 23:07:11 - hyperparameter_tuning - INFO - Trial 2: 发现更好的得分 0.9866
2025-08-08 23:07:11 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 23:07:11 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 23:07:12 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 23:07:12 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9884
2025-08-08 23:07:12 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 23:07:12 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 23:07:12 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 23:07:12 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 23:07:12 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 23:07:12 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 23:07:12 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 23:07:13 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 23:07:13 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 23:07:13 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 23:07:13 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:07:13 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9891
2025-08-08 23:07:13 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:07:13 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9891
2025-08-08 23:07:13 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:07:13 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9891
2025-08-08 23:07:13 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:07:13 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9892
2025-08-08 23:07:14 - hyperparameter_tuning - INFO - Trial 19: 发现更好的得分 0.9899
2025-08-08 23:07:14 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳参数: {'n_estimators': 289, 'max_depth': 4, 'learning_rate': 0.2422517531288597, 'subsample': 0.6595624743036407, 'colsample_bytree': 0.993646027075368}
2025-08-08 23:07:14 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳得分: 0.9899
2025-08-08 23:07:14 - hyperparameter_tuning - INFO - 实际执行试验次数: 20/100
2025-08-08 23:07:14 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-08 23:07:14 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-08 23:07:14 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 23:07:14 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\XGBoost\optimization_history_20250808_230714.html
2025-08-08 23:07:14 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-08 23:07:14 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 23:07:14 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\XGBoost\param_importances_20250808_230714.html
2025-08-08 23:07:14 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 3.67 秒
2025-08-08 23:07:14 - hyperparameter_tuning - INFO - 开始对 LightGBM 进行超参数调优，试验次数: 100
2025-08-08 23:07:14 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 6
2025-08-08 23:07:14 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-08 23:07:14 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-08 23:07:14 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-08 23:07:14 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 100, 'n_jobs': 6, 'timeout': 1800}
2025-08-08 23:07:21 - hyperparameter_tuning - INFO - Trial 4: 发现更好的得分 0.9859
2025-08-08 23:07:21 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:07:21 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9867
2025-08-08 23:07:21 - hyperparameter_tuning - INFO - Trial 11: 发现更好的得分 0.9875
2025-08-08 23:07:22 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳参数: {'n_estimators': 159, 'max_depth': 10, 'learning_rate': 0.17905165423348202, 'feature_fraction': 0.8204385626360735, 'bagging_fraction': 0.6590122802431319}
2025-08-08 23:07:22 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳得分: 0.9875
2025-08-08 23:07:22 - hyperparameter_tuning - INFO - 实际执行试验次数: 16/100
2025-08-08 23:07:22 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-08 23:07:22 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-08 23:07:22 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 23:07:22 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\LightGBM\optimization_history_20250808_230722.html
2025-08-08 23:07:22 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-08 23:07:22 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 23:07:22 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\LightGBM\param_importances_20250808_230722.html
2025-08-08 23:07:22 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 8.12 秒
2025-08-08 23:07:22 - hyperparameter_tuning - INFO - 开始对 CatBoost 进行超参数调优，试验次数: 100
2025-08-08 23:07:22 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 6
2025-08-08 23:07:22 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-08 23:07:22 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-08 23:07:22 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-08 23:07:22 - hyperparameter_tuning - INFO - CatBoost使用串行模式避免GPU设备冲突
2025-08-08 23:07:22 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 100, 'n_jobs': 1, 'timeout': 1800}
2025-08-08 23:07:27 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9721
2025-08-08 23:07:28 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9820
2025-08-08 23:07:42 - hyperparameter_tuning - INFO - Trial 6: 发现更好的得分 0.9861
2025-08-08 23:08:28 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:08:28 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9869
2025-08-08 23:08:28 - hyperparameter_tuning - INFO - 模型 CatBoost 的最佳参数: {'iterations': 204, 'depth': 2, 'learning_rate': 0.11895186568849454, 'l2_leaf_reg': 8.448363213508443, 'bagging_temperature': 0.8095789183531145}
2025-08-08 23:08:28 - hyperparameter_tuning - INFO - 模型 CatBoost 的最佳得分: 0.9869
2025-08-08 23:08:28 - hyperparameter_tuning - INFO - 实际执行试验次数: 17/100
2025-08-08 23:08:28 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-08 23:08:28 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-08 23:08:28 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 23:08:28 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\CatBoost\optimization_history_20250808_230828.html
2025-08-08 23:08:28 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-08 23:08:28 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 23:08:28 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\CatBoost\param_importances_20250808_230828.html
2025-08-08 23:08:28 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 65.92 秒
2025-08-08 23:08:28 - hyperparameter_tuning - INFO - 开始对 Logistic 进行超参数调优，试验次数: 100
2025-08-08 23:08:28 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 6
2025-08-08 23:08:28 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-08 23:08:28 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-08 23:08:28 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-08 23:08:28 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 100, 'n_jobs': 6, 'timeout': 1800}
2025-08-08 23:08:28 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9731
2025-08-08 23:08:29 - hyperparameter_tuning - INFO - Trial 3: 发现更好的得分 0.9747
2025-08-08 23:08:29 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:08:29 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9747
2025-08-08 23:08:29 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:08:29 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9747
2025-08-08 23:08:29 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:08:29 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9747
2025-08-08 23:08:29 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:08:29 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9747
2025-08-08 23:08:29 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:08:29 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:08:29 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9747
2025-08-08 23:08:29 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9747
2025-08-08 23:08:29 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳参数: {'clf__C': 8.884615579781006, 'clf__solver': 'lbfgs'}
2025-08-08 23:08:29 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳得分: 0.9747
2025-08-08 23:08:29 - hyperparameter_tuning - INFO - 实际执行试验次数: 21/100
2025-08-08 23:08:29 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-08 23:08:29 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-08 23:08:29 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 23:08:29 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\Logistic\optimization_history_20250808_230829.html
2025-08-08 23:08:30 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-08 23:08:30 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 23:08:30 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\Logistic\param_importances_20250808_230830.html
2025-08-08 23:08:30 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.43 秒
2025-08-08 23:08:30 - hyperparameter_tuning - INFO - 开始对 SVM 进行超参数调优，试验次数: 100
2025-08-08 23:08:30 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 6
2025-08-08 23:08:30 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-08 23:08:30 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-08 23:08:30 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-08 23:08:30 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 100, 'n_jobs': 6, 'timeout': 1800}
2025-08-08 23:08:30 - hyperparameter_tuning - INFO - Trial 4: 发现更好的得分 0.9859
2025-08-08 23:08:30 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:08:30 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9859
2025-08-08 23:08:30 - hyperparameter_tuning - INFO - Trial 11: 发现更好的得分 0.9876
2025-08-08 23:08:30 - hyperparameter_tuning - INFO - 模型 SVM 的最佳参数: {'clf__C': 4.265370127923194, 'clf__kernel': 'rbf'}
2025-08-08 23:08:30 - hyperparameter_tuning - INFO - 模型 SVM 的最佳得分: 0.9876
2025-08-08 23:08:30 - hyperparameter_tuning - INFO - 实际执行试验次数: 16/100
2025-08-08 23:08:30 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-08 23:08:30 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-08 23:08:31 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 23:08:31 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\SVM\optimization_history_20250808_230830.html
2025-08-08 23:08:31 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-08 23:08:31 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 23:08:31 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\SVM\param_importances_20250808_230831.html
2025-08-08 23:08:31 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.16 秒
2025-08-08 23:08:31 - hyperparameter_tuning - INFO - 开始对 KNN 进行超参数调优，试验次数: 100
2025-08-08 23:08:31 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 6
2025-08-08 23:08:31 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-08 23:08:31 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-08 23:08:31 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-08 23:08:31 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 100, 'n_jobs': 6, 'timeout': 1800}
2025-08-08 23:08:31 - hyperparameter_tuning - INFO - Trial 2: 发现更好的得分 0.9812
2025-08-08 23:08:31 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:08:31 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9816
2025-08-08 23:08:31 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:08:31 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9816
2025-08-08 23:08:31 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:08:31 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9816
2025-08-08 23:08:31 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:08:31 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9816
2025-08-08 23:08:31 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:08:31 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9816
2025-08-08 23:08:31 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:08:31 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9816
2025-08-08 23:08:31 - hyperparameter_tuning - INFO - 模型 KNN 的最佳参数: {'clf__n_neighbors': 4}
2025-08-08 23:08:31 - hyperparameter_tuning - INFO - 模型 KNN 的最佳得分: 0.9816
2025-08-08 23:08:31 - hyperparameter_tuning - INFO - 实际执行试验次数: 16/100
2025-08-08 23:08:31 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-08 23:08:31 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-08 23:08:32 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 23:08:32 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\KNN\optimization_history_20250808_230831.html
2025-08-08 23:08:32 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-08 23:08:32 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 23:08:32 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\KNN\param_importances_20250808_230832.html
2025-08-08 23:08:32 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.08 秒
2025-08-08 23:08:32 - hyperparameter_tuning - INFO - 开始对 NaiveBayes 进行超参数调优，试验次数: 100
2025-08-08 23:08:32 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 6
2025-08-08 23:08:32 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-08 23:08:32 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-08 23:08:32 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-08 23:08:32 - hyperparameter_tuning - INFO - NaiveBayes模型无需调参，计算基准得分
2025-08-08 23:08:32 - hyperparameter_tuning - INFO - NaiveBayes基准得分: 0.9573
2025-08-08 23:08:32 - hyperparameter_tuning - INFO - 开始对 NeuralNet 进行超参数调优，试验次数: 100
2025-08-08 23:08:32 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 6
2025-08-08 23:08:32 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-08 23:08:32 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-08 23:08:32 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-08 23:08:32 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 100, 'n_jobs': 6, 'timeout': 1800}
2025-08-08 23:08:38 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9864
2025-08-08 23:08:48 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:08:48 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9864
2025-08-08 23:08:48 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:08:48 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9864
2025-08-08 23:08:48 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:08:48 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9864
2025-08-08 23:08:49 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:08:49 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9864
2025-08-08 23:08:49 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:08:49 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9864
2025-08-08 23:08:49 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:08:49 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9864
2025-08-08 23:08:49 - hyperparameter_tuning - INFO - 模型 NeuralNet 的最佳参数: {'clf__hidden_layer_sizes': (50,), 'clf__alpha': 0.009952287486232241}
2025-08-08 23:08:49 - hyperparameter_tuning - INFO - 模型 NeuralNet 的最佳得分: 0.9864
2025-08-08 23:08:49 - hyperparameter_tuning - INFO - 实际执行试验次数: 16/100
2025-08-08 23:08:49 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-08 23:08:49 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-08 23:08:49 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 23:08:49 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\NeuralNet\optimization_history_20250808_230849.html
2025-08-08 23:08:49 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-08 23:08:49 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 23:08:49 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\NeuralNet\param_importances_20250808_230849.html
2025-08-08 23:08:49 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 17.15 秒
2025-08-08 23:09:19 - training_session_manager - INFO - 创建会话目录结构: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919
2025-08-08 23:09:19 - training_session_manager - INFO - 创建训练会话: 训练_nodule2_20250808_230919 (ID: 20250808_230919)
2025-08-08 23:09:19 - training_session_manager - INFO - 创建新会话: 训练_nodule2_20250808_230919
2025-08-08 23:09:19 - session_utils - INFO - 创建新会话: 训练_nodule2_20250808_230919 (ID: 20250808_230919)
2025-08-08 23:09:19 - data_preprocessing - INFO - 成功加载 数据，特征维度: (197, 8)
2025-08-08 23:09:19 - data_preprocessing - INFO - 应用了 standard 特征缩放
2025-08-08 23:09:19 - model_training - INFO - 模型名称: Decision Tree
2025-08-08 23:09:19 - model_training - INFO - 准确率: 0.8000
2025-08-08 23:09:19 - model_training - INFO - AUC: 0.8951
2025-08-08 23:09:19 - model_training - INFO - AUPRC: 0.7948
2025-08-08 23:09:19 - model_training - INFO - 混淆矩阵:
2025-08-08 23:09:19 - model_training - INFO - 
[[20  3]
 [ 5 12]]
2025-08-08 23:09:19 - model_training - INFO - 
分类报告:
2025-08-08 23:09:19 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.80      0.87      0.83        23
           1       0.80      0.71      0.75        17

    accuracy                           0.80        40
   macro avg       0.80      0.79      0.79        40
weighted avg       0.80      0.80      0.80        40

2025-08-08 23:09:19 - model_training - INFO - 训练时间: 0.01 秒
2025-08-08 23:09:19 - model_training - INFO - 模型 DecisionTree 性能: 准确率=0.8000
2025-08-08 23:09:19 - training_session_manager - INFO - 保存模型 DecisionTree 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\DecisionTree_single_230919.joblib
2025-08-08 23:09:19 - model_training - INFO - 模型 DecisionTree 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\DecisionTree_single_230919.joblib
2025-08-08 23:09:19 - model_training - INFO - 模型 DecisionTree 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\DecisionTree_results.joblib
2025-08-08 23:09:19 - model_training - INFO - 模型名称: Random Forest
2025-08-08 23:09:19 - model_training - INFO - 准确率: 0.8750
2025-08-08 23:09:19 - model_training - INFO - AUC: 0.9412
2025-08-08 23:09:19 - model_training - INFO - AUPRC: 0.9359
2025-08-08 23:09:19 - model_training - INFO - 混淆矩阵:
2025-08-08 23:09:19 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-08-08 23:09:19 - model_training - INFO - 
分类报告:
2025-08-08 23:09:19 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-08 23:09:19 - model_training - INFO - 训练时间: 0.07 秒
2025-08-08 23:09:19 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.8750
2025-08-08 23:09:19 - training_session_manager - INFO - 保存模型 RandomForest 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\RandomForest_single_230919.joblib
2025-08-08 23:09:19 - model_training - INFO - 模型 RandomForest 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\RandomForest_single_230919.joblib
2025-08-08 23:09:19 - model_training - INFO - 模型 RandomForest 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-08-08 23:09:19 - model_training - INFO - [XGBoost] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-08 23:09:19 - model_training - INFO - 模型名称: XGBoost
2025-08-08 23:09:19 - model_training - INFO - 准确率: 0.9000
2025-08-08 23:09:19 - model_training - INFO - AUC: 0.9719
2025-08-08 23:09:19 - model_training - INFO - AUPRC: 0.9627
2025-08-08 23:09:19 - model_training - INFO - 混淆矩阵:
2025-08-08 23:09:19 - model_training - INFO - 
[[21  2]
 [ 2 15]]
2025-08-08 23:09:19 - model_training - INFO - 
分类报告:
2025-08-08 23:09:19 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.91      0.91      0.91        23
           1       0.88      0.88      0.88        17

    accuracy                           0.90        40
   macro avg       0.90      0.90      0.90        40
weighted avg       0.90      0.90      0.90        40

2025-08-08 23:09:19 - model_training - INFO - 训练时间: 0.05 秒
2025-08-08 23:09:19 - model_training - INFO - 模型 XGBoost 性能: 准确率=0.9000
2025-08-08 23:09:19 - training_session_manager - INFO - 保存模型 XGBoost 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\XGBoost_single_230919.joblib
2025-08-08 23:09:19 - model_training - INFO - 模型 XGBoost 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\XGBoost_single_230919.joblib
2025-08-08 23:09:19 - model_training - INFO - 模型 XGBoost 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_results.joblib
2025-08-08 23:09:19 - model_training - INFO - [LightGBM] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-08 23:09:19 - model_training - INFO - 模型名称: LightGBM
2025-08-08 23:09:19 - model_training - INFO - 准确率: 0.8500
2025-08-08 23:09:19 - model_training - INFO - AUC: 0.9488
2025-08-08 23:09:19 - model_training - INFO - AUPRC: 0.9492
2025-08-08 23:09:19 - model_training - INFO - 混淆矩阵:
2025-08-08 23:09:19 - model_training - INFO - 
[[20  3]
 [ 3 14]]
2025-08-08 23:09:19 - model_training - INFO - 
分类报告:
2025-08-08 23:09:19 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.87      0.87      0.87        23
           1       0.82      0.82      0.82        17

    accuracy                           0.85        40
   macro avg       0.85      0.85      0.85        40
weighted avg       0.85      0.85      0.85        40

2025-08-08 23:09:19 - model_training - INFO - 训练时间: 0.03 秒
2025-08-08 23:09:19 - model_training - INFO - 模型 LightGBM 性能: 准确率=0.8500
2025-08-08 23:09:19 - training_session_manager - INFO - 保存模型 LightGBM 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\LightGBM_single_230919.joblib
2025-08-08 23:09:19 - model_training - INFO - 模型 LightGBM 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\LightGBM_single_230919.joblib
2025-08-08 23:09:19 - model_training - INFO - 模型 LightGBM 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_results.joblib
2025-08-08 23:09:19 - model_training - INFO - [CatBoost] 启用 auto_class_weights='Balanced' 以处理不平衡
2025-08-08 23:09:20 - model_training - INFO - 模型名称: CatBoost
2025-08-08 23:09:20 - model_training - INFO - 准确率: 0.8750
2025-08-08 23:09:20 - model_training - INFO - AUC: 0.9591
2025-08-08 23:09:20 - model_training - INFO - AUPRC: 0.9570
2025-08-08 23:09:20 - model_training - INFO - 混淆矩阵:
2025-08-08 23:09:20 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-08-08 23:09:20 - model_training - INFO - 
分类报告:
2025-08-08 23:09:20 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-08 23:09:20 - model_training - INFO - 训练时间: 1.02 秒
2025-08-08 23:09:20 - model_training - INFO - 模型 CatBoost 性能: 准确率=0.8750
2025-08-08 23:09:20 - training_session_manager - INFO - 保存模型 CatBoost 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\CatBoost_single_230920.joblib
2025-08-08 23:09:20 - model_training - INFO - 模型 CatBoost 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\CatBoost_single_230920.joblib
2025-08-08 23:09:20 - model_training - INFO - 模型 CatBoost 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\CatBoost_results.joblib
2025-08-08 23:09:20 - model_training - INFO - 模型名称: Logistic Regression
2025-08-08 23:09:20 - model_training - INFO - 准确率: 0.8250
2025-08-08 23:09:20 - model_training - INFO - AUC: 0.9284
2025-08-08 23:09:20 - model_training - INFO - AUPRC: 0.9288
2025-08-08 23:09:20 - model_training - INFO - 混淆矩阵:
2025-08-08 23:09:20 - model_training - INFO - 
[[20  3]
 [ 4 13]]
2025-08-08 23:09:20 - model_training - INFO - 
分类报告:
2025-08-08 23:09:20 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.83      0.87      0.85        23
           1       0.81      0.76      0.79        17

    accuracy                           0.82        40
   macro avg       0.82      0.82      0.82        40
weighted avg       0.82      0.82      0.82        40

2025-08-08 23:09:20 - model_training - INFO - 训练时间: 0.02 秒
2025-08-08 23:09:20 - model_training - INFO - 模型 Logistic 性能: 准确率=0.8250
2025-08-08 23:09:20 - training_session_manager - INFO - 保存模型 Logistic 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\Logistic_single_230920.joblib
2025-08-08 23:09:20 - model_training - INFO - 模型 Logistic 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\Logistic_single_230920.joblib
2025-08-08 23:09:20 - model_training - INFO - 模型 Logistic 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_results.joblib
2025-08-08 23:09:20 - model_training - INFO - 模型名称: SVM
2025-08-08 23:09:20 - model_training - INFO - 准确率: 0.7750
2025-08-08 23:09:20 - model_training - INFO - AUC: 0.9182
2025-08-08 23:09:20 - model_training - INFO - AUPRC: 0.9034
2025-08-08 23:09:20 - model_training - INFO - 混淆矩阵:
2025-08-08 23:09:20 - model_training - INFO - 
[[19  4]
 [ 5 12]]
2025-08-08 23:09:20 - model_training - INFO - 
分类报告:
2025-08-08 23:09:20 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.79      0.83      0.81        23
           1       0.75      0.71      0.73        17

    accuracy                           0.78        40
   macro avg       0.77      0.77      0.77        40
weighted avg       0.77      0.78      0.77        40

2025-08-08 23:09:20 - model_training - INFO - 训练时间: 0.02 秒
2025-08-08 23:09:20 - model_training - INFO - 模型 SVM 性能: 准确率=0.7750
2025-08-08 23:09:20 - training_session_manager - INFO - 保存模型 SVM 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\SVM_single_230920.joblib
2025-08-08 23:09:20 - model_training - INFO - 模型 SVM 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\SVM_single_230920.joblib
2025-08-08 23:09:21 - model_training - INFO - 模型 SVM 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\SVM_results.joblib
2025-08-08 23:09:21 - model_training - INFO - 模型名称: KNN
2025-08-08 23:09:21 - model_training - INFO - 准确率: 0.8750
2025-08-08 23:09:21 - model_training - INFO - AUC: 0.9322
2025-08-08 23:09:21 - model_training - INFO - AUPRC: 0.9189
2025-08-08 23:09:21 - model_training - INFO - 混淆矩阵:
2025-08-08 23:09:21 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-08-08 23:09:21 - model_training - INFO - 
分类报告:
2025-08-08 23:09:21 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-08 23:09:21 - model_training - INFO - 训练时间: 0.02 秒
2025-08-08 23:09:21 - model_training - INFO - 模型 KNN 性能: 准确率=0.8750
2025-08-08 23:09:21 - training_session_manager - INFO - 保存模型 KNN 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\KNN_single_230921.joblib
2025-08-08 23:09:21 - model_training - INFO - 模型 KNN 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\KNN_single_230921.joblib
2025-08-08 23:09:21 - model_training - INFO - 模型 KNN 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\KNN_results.joblib
2025-08-08 23:09:21 - model_training - INFO - 模型名称: Naive Bayes
2025-08-08 23:09:21 - model_training - INFO - 准确率: 0.8750
2025-08-08 23:09:21 - model_training - INFO - AUC: 0.8977
2025-08-08 23:09:21 - model_training - INFO - AUPRC: 0.9096
2025-08-08 23:09:21 - model_training - INFO - 混淆矩阵:
2025-08-08 23:09:21 - model_training - INFO - 
[[22  1]
 [ 4 13]]
2025-08-08 23:09:21 - model_training - INFO - 
分类报告:
2025-08-08 23:09:21 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.85      0.96      0.90        23
           1       0.93      0.76      0.84        17

    accuracy                           0.88        40
   macro avg       0.89      0.86      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-08 23:09:21 - model_training - INFO - 训练时间: 0.00 秒
2025-08-08 23:09:21 - model_training - INFO - 模型 NaiveBayes 性能: 准确率=0.8750
2025-08-08 23:09:21 - training_session_manager - INFO - 保存模型 NaiveBayes 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\NaiveBayes_single_230921.joblib
2025-08-08 23:09:21 - model_training - INFO - 模型 NaiveBayes 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\NaiveBayes_single_230921.joblib
2025-08-08 23:09:21 - model_training - INFO - 模型 NaiveBayes 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NaiveBayes_results.joblib
2025-08-08 23:09:21 - model_training - INFO - 模型名称: Neural Network
2025-08-08 23:09:21 - model_training - INFO - 准确率: 0.8750
2025-08-08 23:09:21 - model_training - INFO - AUC: 0.9591
2025-08-08 23:09:21 - model_training - INFO - AUPRC: 0.9450
2025-08-08 23:09:21 - model_training - INFO - 混淆矩阵:
2025-08-08 23:09:21 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-08-08 23:09:21 - model_training - INFO - 
分类报告:
2025-08-08 23:09:21 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-08 23:09:21 - model_training - INFO - 训练时间: 0.24 秒
2025-08-08 23:09:21 - model_training - INFO - 模型 NeuralNet 性能: 准确率=0.8750
2025-08-08 23:09:21 - training_session_manager - INFO - 保存模型 NeuralNet 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\NeuralNet_single_230921.joblib
2025-08-08 23:09:21 - model_training - INFO - 模型 NeuralNet 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\NeuralNet_single_230921.joblib
2025-08-08 23:09:21 - model_training - INFO - 模型 NeuralNet 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NeuralNet_results.joblib
2025-08-08 23:12:11 - model_ensemble - INFO - ============================================================
2025-08-08 23:12:11 - model_ensemble - INFO - 开始运行集成学习管道
2025-08-08 23:12:11 - model_ensemble - INFO - ============================================================
2025-08-08 23:12:11 - model_ensemble - INFO - 基础模型: ['DecisionTree', 'RandomForest', 'XGBoost', 'LightGBM', 'CatBoost', 'Logistic', 'SVM', 'KNN', 'NaiveBayes', 'NeuralNet']
2025-08-08 23:12:11 - model_ensemble - INFO - 集成方法: ['stacking']
2025-08-08 23:12:11 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-08-08 23:12:11 - model_ensemble - INFO - 训练基础模型: DecisionTree
2025-08-08 23:12:11 - model_training - INFO - 模型名称: Decision Tree
2025-08-08 23:12:11 - model_training - INFO - 准确率: 0.8000
2025-08-08 23:12:11 - model_training - INFO - AUC: 0.8951
2025-08-08 23:12:11 - model_training - INFO - AUPRC: 0.7948
2025-08-08 23:12:11 - model_training - INFO - 混淆矩阵:
2025-08-08 23:12:11 - model_training - INFO - 
[[20  3]
 [ 5 12]]
2025-08-08 23:12:11 - model_training - INFO - 
分类报告:
2025-08-08 23:12:11 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.80      0.87      0.83        23
           1       0.80      0.71      0.75        17

    accuracy                           0.80        40
   macro avg       0.80      0.79      0.79        40
weighted avg       0.80      0.80      0.80        40

2025-08-08 23:12:11 - model_training - INFO - 训练时间: 0.00 秒
2025-08-08 23:12:11 - model_training - INFO - 模型 DecisionTree 性能: 准确率=0.8000
2025-08-08 23:12:11 - training_session_manager - INFO - 保存模型 DecisionTree 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\DecisionTree_single_231211.joblib
2025-08-08 23:12:11 - model_training - INFO - 模型 DecisionTree 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\DecisionTree_single_231211.joblib
2025-08-08 23:12:11 - model_training - INFO - 模型 DecisionTree 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\DecisionTree_results.joblib
2025-08-08 23:12:11 - model_ensemble - INFO -   DecisionTree 训练完成
2025-08-08 23:12:11 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-08-08 23:12:11 - model_training - INFO - 模型名称: Random Forest
2025-08-08 23:12:11 - model_training - INFO - 准确率: 0.8750
2025-08-08 23:12:11 - model_training - INFO - AUC: 0.9412
2025-08-08 23:12:11 - model_training - INFO - AUPRC: 0.9359
2025-08-08 23:12:11 - model_training - INFO - 混淆矩阵:
2025-08-08 23:12:11 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-08-08 23:12:11 - model_training - INFO - 
分类报告:
2025-08-08 23:12:11 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-08 23:12:11 - model_training - INFO - 训练时间: 0.08 秒
2025-08-08 23:12:11 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.8750
2025-08-08 23:12:11 - training_session_manager - INFO - 保存模型 RandomForest 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\RandomForest_single_231211.joblib
2025-08-08 23:12:11 - model_training - INFO - 模型 RandomForest 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\RandomForest_single_231211.joblib
2025-08-08 23:12:11 - model_training - INFO - 模型 RandomForest 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-08-08 23:12:11 - model_ensemble - INFO -   RandomForest 训练完成
2025-08-08 23:12:11 - model_ensemble - INFO - 训练基础模型: XGBoost
2025-08-08 23:12:11 - model_training - INFO - [XGBoost] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-08 23:12:11 - model_training - INFO - 模型名称: XGBoost
2025-08-08 23:12:11 - model_training - INFO - 准确率: 0.9000
2025-08-08 23:12:11 - model_training - INFO - AUC: 0.9719
2025-08-08 23:12:11 - model_training - INFO - AUPRC: 0.9627
2025-08-08 23:12:11 - model_training - INFO - 混淆矩阵:
2025-08-08 23:12:11 - model_training - INFO - 
[[21  2]
 [ 2 15]]
2025-08-08 23:12:11 - model_training - INFO - 
分类报告:
2025-08-08 23:12:11 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.91      0.91      0.91        23
           1       0.88      0.88      0.88        17

    accuracy                           0.90        40
   macro avg       0.90      0.90      0.90        40
weighted avg       0.90      0.90      0.90        40

2025-08-08 23:12:11 - model_training - INFO - 训练时间: 0.04 秒
2025-08-08 23:12:11 - model_training - INFO - 模型 XGBoost 性能: 准确率=0.9000
2025-08-08 23:12:11 - training_session_manager - INFO - 保存模型 XGBoost 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\XGBoost_single_231211.joblib
2025-08-08 23:12:11 - model_training - INFO - 模型 XGBoost 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\XGBoost_single_231211.joblib
2025-08-08 23:12:11 - model_training - INFO - 模型 XGBoost 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_results.joblib
2025-08-08 23:12:11 - model_ensemble - INFO -   XGBoost 训练完成
2025-08-08 23:12:11 - model_ensemble - INFO - 训练基础模型: LightGBM
2025-08-08 23:12:11 - model_training - INFO - [LightGBM] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-08 23:12:11 - model_training - INFO - 模型名称: LightGBM
2025-08-08 23:12:11 - model_training - INFO - 准确率: 0.8500
2025-08-08 23:12:11 - model_training - INFO - AUC: 0.9488
2025-08-08 23:12:11 - model_training - INFO - AUPRC: 0.9492
2025-08-08 23:12:11 - model_training - INFO - 混淆矩阵:
2025-08-08 23:12:11 - model_training - INFO - 
[[20  3]
 [ 3 14]]
2025-08-08 23:12:11 - model_training - INFO - 
分类报告:
2025-08-08 23:12:11 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.87      0.87      0.87        23
           1       0.82      0.82      0.82        17

    accuracy                           0.85        40
   macro avg       0.85      0.85      0.85        40
weighted avg       0.85      0.85      0.85        40

2025-08-08 23:12:11 - model_training - INFO - 训练时间: 0.04 秒
2025-08-08 23:12:11 - model_training - INFO - 模型 LightGBM 性能: 准确率=0.8500
2025-08-08 23:12:11 - training_session_manager - INFO - 保存模型 LightGBM 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\LightGBM_single_231211.joblib
2025-08-08 23:12:11 - model_training - INFO - 模型 LightGBM 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\LightGBM_single_231211.joblib
2025-08-08 23:12:11 - model_training - INFO - 模型 LightGBM 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_results.joblib
2025-08-08 23:12:11 - model_ensemble - INFO -   LightGBM 训练完成
2025-08-08 23:12:11 - model_ensemble - INFO - 训练基础模型: CatBoost
2025-08-08 23:12:11 - model_training - INFO - [CatBoost] 启用 auto_class_weights='Balanced' 以处理不平衡
2025-08-08 23:12:12 - model_training - INFO - 模型名称: CatBoost
2025-08-08 23:12:12 - model_training - INFO - 准确率: 0.8750
2025-08-08 23:12:12 - model_training - INFO - AUC: 0.9591
2025-08-08 23:12:12 - model_training - INFO - AUPRC: 0.9570
2025-08-08 23:12:12 - model_training - INFO - 混淆矩阵:
2025-08-08 23:12:12 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-08-08 23:12:12 - model_training - INFO - 
分类报告:
2025-08-08 23:12:12 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-08 23:12:12 - model_training - INFO - 训练时间: 1.04 秒
2025-08-08 23:12:12 - model_training - INFO - 模型 CatBoost 性能: 准确率=0.8750
2025-08-08 23:12:12 - training_session_manager - INFO - 保存模型 CatBoost 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\CatBoost_single_231212.joblib
2025-08-08 23:12:12 - model_training - INFO - 模型 CatBoost 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\CatBoost_single_231212.joblib
2025-08-08 23:12:12 - model_training - INFO - 模型 CatBoost 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\CatBoost_results.joblib
2025-08-08 23:12:12 - model_ensemble - INFO -   CatBoost 训练完成
2025-08-08 23:12:12 - model_ensemble - INFO - 训练基础模型: Logistic
2025-08-08 23:12:12 - model_training - INFO - 模型名称: Logistic Regression
2025-08-08 23:12:12 - model_training - INFO - 准确率: 0.8250
2025-08-08 23:12:12 - model_training - INFO - AUC: 0.9284
2025-08-08 23:12:12 - model_training - INFO - AUPRC: 0.9288
2025-08-08 23:12:12 - model_training - INFO - 混淆矩阵:
2025-08-08 23:12:12 - model_training - INFO - 
[[20  3]
 [ 4 13]]
2025-08-08 23:12:12 - model_training - INFO - 
分类报告:
2025-08-08 23:12:12 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.83      0.87      0.85        23
           1       0.81      0.76      0.79        17

    accuracy                           0.82        40
   macro avg       0.82      0.82      0.82        40
weighted avg       0.82      0.82      0.82        40

2025-08-08 23:12:12 - model_training - INFO - 训练时间: 0.01 秒
2025-08-08 23:12:12 - model_training - INFO - 模型 Logistic 性能: 准确率=0.8250
2025-08-08 23:12:12 - training_session_manager - INFO - 保存模型 Logistic 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\Logistic_single_231212.joblib
2025-08-08 23:12:12 - model_training - INFO - 模型 Logistic 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\Logistic_single_231212.joblib
2025-08-08 23:12:12 - model_training - INFO - 模型 Logistic 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_results.joblib
2025-08-08 23:12:12 - model_ensemble - INFO -   Logistic 训练完成
2025-08-08 23:12:12 - model_ensemble - INFO - 训练基础模型: SVM
2025-08-08 23:12:12 - model_training - INFO - 模型名称: SVM
2025-08-08 23:12:12 - model_training - INFO - 准确率: 0.7750
2025-08-08 23:12:12 - model_training - INFO - AUC: 0.9182
2025-08-08 23:12:12 - model_training - INFO - AUPRC: 0.9034
2025-08-08 23:12:12 - model_training - INFO - 混淆矩阵:
2025-08-08 23:12:12 - model_training - INFO - 
[[19  4]
 [ 5 12]]
2025-08-08 23:12:12 - model_training - INFO - 
分类报告:
2025-08-08 23:12:12 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.79      0.83      0.81        23
           1       0.75      0.71      0.73        17

    accuracy                           0.78        40
   macro avg       0.77      0.77      0.77        40
weighted avg       0.77      0.78      0.77        40

2025-08-08 23:12:12 - model_training - INFO - 训练时间: 0.01 秒
2025-08-08 23:12:12 - model_training - INFO - 模型 SVM 性能: 准确率=0.7750
2025-08-08 23:12:12 - training_session_manager - INFO - 保存模型 SVM 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\SVM_single_231212.joblib
2025-08-08 23:12:12 - model_training - INFO - 模型 SVM 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\SVM_single_231212.joblib
2025-08-08 23:12:12 - model_training - INFO - 模型 SVM 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\SVM_results.joblib
2025-08-08 23:12:12 - model_ensemble - INFO -   SVM 训练完成
2025-08-08 23:12:12 - model_ensemble - INFO - 训练基础模型: KNN
2025-08-08 23:12:12 - model_training - INFO - 模型名称: KNN
2025-08-08 23:12:12 - model_training - INFO - 准确率: 0.8750
2025-08-08 23:12:12 - model_training - INFO - AUC: 0.9322
2025-08-08 23:12:12 - model_training - INFO - AUPRC: 0.9189
2025-08-08 23:12:12 - model_training - INFO - 混淆矩阵:
2025-08-08 23:12:12 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-08-08 23:12:12 - model_training - INFO - 
分类报告:
2025-08-08 23:12:12 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-08 23:12:12 - model_training - INFO - 训练时间: 0.02 秒
2025-08-08 23:12:12 - model_training - INFO - 模型 KNN 性能: 准确率=0.8750
2025-08-08 23:12:12 - training_session_manager - INFO - 保存模型 KNN 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\KNN_single_231212.joblib
2025-08-08 23:12:12 - model_training - INFO - 模型 KNN 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\KNN_single_231212.joblib
2025-08-08 23:12:12 - model_training - INFO - 模型 KNN 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\KNN_results.joblib
2025-08-08 23:12:12 - model_ensemble - INFO -   KNN 训练完成
2025-08-08 23:12:12 - model_ensemble - INFO - 训练基础模型: NaiveBayes
2025-08-08 23:12:12 - model_training - INFO - 模型名称: Naive Bayes
2025-08-08 23:12:12 - model_training - INFO - 准确率: 0.8750
2025-08-08 23:12:12 - model_training - INFO - AUC: 0.8977
2025-08-08 23:12:12 - model_training - INFO - AUPRC: 0.9096
2025-08-08 23:12:12 - model_training - INFO - 混淆矩阵:
2025-08-08 23:12:12 - model_training - INFO - 
[[22  1]
 [ 4 13]]
2025-08-08 23:12:12 - model_training - INFO - 
分类报告:
2025-08-08 23:12:12 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.85      0.96      0.90        23
           1       0.93      0.76      0.84        17

    accuracy                           0.88        40
   macro avg       0.89      0.86      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-08 23:12:12 - model_training - INFO - 训练时间: 0.01 秒
2025-08-08 23:12:12 - model_training - INFO - 模型 NaiveBayes 性能: 准确率=0.8750
2025-08-08 23:12:12 - training_session_manager - INFO - 保存模型 NaiveBayes 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\NaiveBayes_single_231212.joblib
2025-08-08 23:12:12 - model_training - INFO - 模型 NaiveBayes 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\NaiveBayes_single_231212.joblib
2025-08-08 23:12:12 - model_training - INFO - 模型 NaiveBayes 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NaiveBayes_results.joblib
2025-08-08 23:12:12 - model_ensemble - INFO -   NaiveBayes 训练完成
2025-08-08 23:12:12 - model_ensemble - INFO - 训练基础模型: NeuralNet
2025-08-08 23:12:13 - model_training - INFO - 模型名称: Neural Network
2025-08-08 23:12:13 - model_training - INFO - 准确率: 0.8750
2025-08-08 23:12:13 - model_training - INFO - AUC: 0.9591
2025-08-08 23:12:13 - model_training - INFO - AUPRC: 0.9450
2025-08-08 23:12:13 - model_training - INFO - 混淆矩阵:
2025-08-08 23:12:13 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-08-08 23:12:13 - model_training - INFO - 
分类报告:
2025-08-08 23:12:13 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-08 23:12:13 - model_training - INFO - 训练时间: 0.24 秒
2025-08-08 23:12:13 - model_training - INFO - 模型 NeuralNet 性能: 准确率=0.8750
2025-08-08 23:12:13 - training_session_manager - INFO - 保存模型 NeuralNet 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\NeuralNet_single_231213.joblib
2025-08-08 23:12:13 - model_training - INFO - 模型 NeuralNet 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\NeuralNet_single_231213.joblib
2025-08-08 23:12:13 - model_training - INFO - 模型 NeuralNet 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NeuralNet_results.joblib
2025-08-08 23:12:13 - model_ensemble - INFO -   NeuralNet 训练完成
2025-08-08 23:12:13 - model_ensemble - INFO - 成功训练了 10 个基础模型
2025-08-08 23:12:13 - model_ensemble - INFO - 步骤2: 运行集成方法 - stacking
2025-08-08 23:12:13 - model_ensemble - INFO - 开始训练集成模型，方法: stacking
2025-08-08 23:12:20 - model_ensemble - INFO -   stacking - 准确率: 0.8750, F1: 0.8744
2025-08-08 23:12:20 - model_ensemble - INFO - ============================================================
2025-08-08 23:12:20 - model_ensemble - INFO - 集成学习结果总结
2025-08-08 23:12:20 - model_ensemble - INFO - ============================================================
2025-08-08 23:12:20 - model_ensemble - INFO - 最佳集成模型: stacking
2025-08-08 23:12:20 - model_ensemble - INFO - 最佳F1分数: 0.8744
2025-08-08 23:12:20 - model_ensemble - INFO - 最佳准确率: 0.8750
2025-08-08 23:12:20 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-08-08 23:12:20 - model_ensemble - INFO -   stacking        - 准确率: 0.8750, 精确率: 0.8750, 召回率: 0.8750, F1: 0.8744, AUC: 0.9591
2025-08-08 23:12:20 - model_ensemble - INFO - 步骤3: 保存集成学习结果
2025-08-08 23:12:20 - training_session_manager - INFO - 保存模型 ensemble_20250808_231220 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\ensemble_20250808_231220_ensemble_231220.joblib
2025-08-08 23:12:20 - model_ensemble - INFO - 集成学习结果已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\ensemble_20250808_231220_ensemble_231220.joblib
2025-08-08 23:12:22 - model_ensemble - WARNING - Failed to save ensemble report: main thread is not in main loop
2025-08-08 23:12:27 - safe_visualization - INFO - Safe matplotlib configuration applied
2025-08-08 23:12:27 - safe_visualization - INFO - Safe matplotlib configuration applied
2025-08-08 23:12:28 - safe_visualization - INFO - Ensemble performance plot saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\visualizations\ensemble_performance.png
2025-08-08 23:12:28 - safe_visualization - INFO - Summary report saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\visualizations\ensemble_summary_report.txt
2025-08-08 23:12:28 - shap - INFO - num_full_subsets = 4
2025-08-08 23:12:28 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-08-08 23:12:28 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-08-08 23:12:28 - shap - INFO - phi = array([-0.00355635,  0.07342364, -0.00128623,  0.00158387, -0.00820595,
        0.19685017,  0.00686393, -0.0005195 ])
2025-08-08 23:12:28 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-08-08 23:12:28 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-08-08 23:12:28 - shap - INFO - phi = array([ 0.00355635, -0.07342364,  0.00128623, -0.00158387,  0.00820595,
       -0.19685017, -0.00686393,  0.0005195 ])
2025-08-08 23:12:28 - shap - INFO - num_full_subsets = 4
2025-08-08 23:12:28 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-08-08 23:12:28 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-08-08 23:12:28 - shap - INFO - phi = array([-3.55897907e-03,  7.74277047e-02, -1.28154815e-03,  2.52009358e-03,
       -6.02966377e-03,  2.01668829e-01, -4.80032340e-03, -1.13419250e-04])
2025-08-08 23:12:28 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-08-08 23:12:28 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-08-08 23:12:28 - shap - INFO - phi = array([ 3.55897907e-03, -7.74277047e-02,  1.28154815e-03, -2.52009358e-03,
        6.02966377e-03, -2.01668829e-01,  4.80032340e-03,  1.13419250e-04])
2025-08-08 23:12:28 - shap - INFO - num_full_subsets = 4
2025-08-08 23:12:28 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-08-08 23:12:28 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-08-08 23:12:28 - shap - INFO - phi = array([ 0.00536829, -0.06458139,  0.0008487 , -0.00097708, -0.04200947,
        0.12603325,  0.00554068, -0.00048673])
2025-08-08 23:12:28 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-08-08 23:12:28 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-08-08 23:12:28 - shap - INFO - phi = array([-0.00536829,  0.06458139, -0.0008487 ,  0.00097708,  0.04200947,
       -0.12603325, -0.00554068,  0.00048673])
2025-08-08 23:12:28 - shap - INFO - num_full_subsets = 4
2025-08-08 23:12:28 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-08-08 23:12:28 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-08-08 23:12:28 - shap - INFO - phi = array([ 0.00526978, -0.07409138,  0.00059355, -0.00099042, -0.04482853,
        0.12546562,  0.01890768, -0.00095738])
2025-08-08 23:12:28 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-08-08 23:12:28 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-08-08 23:12:28 - shap - INFO - phi = array([-0.00526978,  0.07409138, -0.00059355,  0.00099042,  0.04482853,
       -0.12546562, -0.01890768,  0.00095738])
2025-08-08 23:12:28 - shap - INFO - num_full_subsets = 4
2025-08-08 23:12:28 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-08-08 23:12:28 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-08-08 23:12:28 - shap - INFO - phi = array([-3.39064305e-03,  2.73611599e-02, -1.19066996e-03, -6.23926397e-04,
       -2.33913512e-03,  2.72618123e-01, -2.37811643e-04, -4.31897671e-03])
2025-08-08 23:12:28 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-08-08 23:12:28 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-08-08 23:12:28 - shap - INFO - phi = array([ 3.39064305e-03, -2.73611599e-02,  1.19066996e-03,  6.23926397e-04,
        2.33913512e-03, -2.72618123e-01,  2.37811643e-04,  4.31897671e-03])
2025-08-08 23:12:28 - shap - INFO - num_full_subsets = 4
2025-08-08 23:12:28 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-08-08 23:12:28 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-08-08 23:12:28 - shap - INFO - phi = array([-0.00367303,  0.07526057, -0.00130701, -0.00067323, -0.00058085,
        0.20044164, -0.00440646, -0.00212885])
2025-08-08 23:12:28 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-08-08 23:12:28 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-08-08 23:12:28 - shap - INFO - phi = array([ 0.00367303, -0.07526057,  0.00130701,  0.00067323,  0.00058085,
       -0.20044164,  0.00440646,  0.00212885])
2025-08-08 23:12:28 - shap - INFO - num_full_subsets = 4
2025-08-08 23:12:29 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-08-08 23:12:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-08-08 23:12:29 - shap - INFO - phi = array([ 0.00523073, -0.07316179,  0.00094751,  0.00057288, -0.05284859,
        0.12595213,  0.02440071,  0.00104974])
2025-08-08 23:12:29 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-08-08 23:12:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-08-08 23:12:29 - shap - INFO - phi = array([-0.00523073,  0.07316179, -0.00094751, -0.00057288,  0.05284859,
       -0.12595213, -0.02440071, -0.00104974])
2025-08-08 23:12:29 - shap - INFO - num_full_subsets = 4
2025-08-08 23:12:29 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-08-08 23:12:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-08-08 23:12:29 - shap - INFO - phi = array([ 0.00243395,  0.00851875,  0.00043318,  0.00252802, -0.03531808,
        0.28538386,  0.03262822, -0.00183706])
2025-08-08 23:12:29 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-08-08 23:12:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-08-08 23:12:29 - shap - INFO - phi = array([-0.00243395, -0.00851875, -0.00043318, -0.00252802,  0.03531808,
       -0.28538386, -0.03262822,  0.00183706])
2025-08-08 23:12:29 - shap - INFO - num_full_subsets = 4
2025-08-08 23:12:29 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-08-08 23:12:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-08-08 23:12:29 - shap - INFO - phi = array([ 0.0025883 , -0.10621072,  0.00149762, -0.00087713, -0.08033543,
       -0.2686836 ,  0.02465215,  0.0023608 ])
2025-08-08 23:12:29 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-08-08 23:12:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-08-08 23:12:29 - shap - INFO - phi = array([-0.0025883 ,  0.10621072, -0.00149762,  0.00087713,  0.08033543,
        0.2686836 , -0.02465215, -0.0023608 ])
2025-08-08 23:12:29 - shap - INFO - num_full_subsets = 4
2025-08-08 23:12:29 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-08-08 23:12:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-08-08 23:12:29 - shap - INFO - phi = array([-0.00334419,  0.03437119, -0.0006048 , -0.00044087,  0.01347819,
        0.25840808, -0.0129795 , -0.00038854])
2025-08-08 23:12:29 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-08-08 23:12:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-08-08 23:12:29 - shap - INFO - phi = array([ 0.00334419, -0.03437119,  0.0006048 ,  0.00044087, -0.01347819,
       -0.25840808,  0.0129795 ,  0.00038854])
2025-08-08 23:12:29 - shap - INFO - num_full_subsets = 4
2025-08-08 23:12:29 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-08-08 23:12:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-08-08 23:12:29 - shap - INFO - phi = array([-0.00309358,  0.02132787, -0.00098244, -0.00058215, -0.00526608,
        0.26988949,  0.00747393, -0.00079778])
2025-08-08 23:12:29 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-08-08 23:12:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-08-08 23:12:29 - shap - INFO - phi = array([ 0.00309358, -0.02132787,  0.00098244,  0.00058215,  0.00526608,
       -0.26988949, -0.00747393,  0.00079778])
2025-08-08 23:12:29 - shap - INFO - num_full_subsets = 4
2025-08-08 23:12:29 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-08-08 23:12:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-08-08 23:12:29 - shap - INFO - phi = array([ 0.00519892, -0.06344263,  0.00059794,  0.00089898, -0.06391689,
        0.12512031,  0.0266728 ,  0.00102971])
2025-08-08 23:12:29 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-08-08 23:12:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-08-08 23:12:29 - shap - INFO - phi = array([-0.00519892,  0.06344263, -0.00059794, -0.00089898,  0.06391689,
       -0.12512031, -0.0266728 , -0.00102971])
2025-08-08 23:12:29 - shap - INFO - num_full_subsets = 4
2025-08-08 23:12:29 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-08-08 23:12:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-08-08 23:12:29 - shap - INFO - phi = array([-0.00375607,  0.0754585 , -0.00132104, -0.00055078, -0.0002321 ,
        0.19926296, -0.00535569, -0.00036815])
2025-08-08 23:12:29 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-08-08 23:12:29 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-08-08 23:12:29 - shap - INFO - phi = array([ 0.00375607, -0.0754585 ,  0.00132104,  0.00055078,  0.0002321 ,
       -0.19926296,  0.00535569,  0.00036815])
2025-08-08 23:12:29 - shap - INFO - num_full_subsets = 4
2025-08-08 23:12:30 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-08-08 23:12:30 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-08-08 23:12:30 - shap - INFO - phi = array([ 0.00247825, -0.06124182,  0.00118401, -0.00056951,  0.02919592,
       -0.39805993,  0.00104332,  0.0010159 ])
2025-08-08 23:12:30 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-08-08 23:12:30 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-08-08 23:12:30 - shap - INFO - phi = array([-0.00247825,  0.06124182, -0.00118401,  0.00056951, -0.02919592,
        0.39805993, -0.00104332, -0.0010159 ])
2025-08-08 23:12:30 - shap - INFO - num_full_subsets = 4
2025-08-08 23:12:30 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-08-08 23:12:30 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-08-08 23:12:30 - shap - INFO - phi = array([-3.22289017e-03,  8.27981367e-02, -2.13488560e-04,  2.39591413e-03,
       -3.23863834e-02,  2.43412618e-01, -2.20001240e-03, -3.08628184e-04])
2025-08-08 23:12:30 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-08-08 23:12:30 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-08-08 23:12:30 - shap - INFO - phi = array([ 3.22289017e-03, -8.27981367e-02,  2.13488560e-04, -2.39591413e-03,
        3.23863834e-02, -2.43412618e-01,  2.20001240e-03,  3.08628184e-04])
2025-08-08 23:12:30 - shap - INFO - num_full_subsets = 4
2025-08-08 23:12:30 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-08-08 23:12:30 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-08-08 23:12:30 - shap - INFO - phi = array([-3.23849348e-03, -6.90879453e-02,  1.37940863e-03, -5.90358534e-04,
        2.04401032e-02, -3.29729199e-01, -4.85071914e-02, -1.61634877e-04])
2025-08-08 23:12:30 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-08-08 23:12:30 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-08-08 23:12:30 - shap - INFO - phi = array([ 3.23849348e-03,  6.90879453e-02, -1.37940863e-03,  5.90358534e-04,
       -2.04401032e-02,  3.29729199e-01,  4.85071914e-02,  1.61634877e-04])
2025-08-08 23:12:30 - shap - INFO - num_full_subsets = 4
2025-08-08 23:12:30 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-08-08 23:12:30 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-08-08 23:12:30 - shap - INFO - phi = array([-0.00333723,  0.10068128, -0.00118084, -0.00055984,  0.00811506,
        0.19697376, -0.01185539, -0.00096166])
2025-08-08 23:12:30 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-08-08 23:12:30 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-08-08 23:12:30 - shap - INFO - phi = array([ 0.00333723, -0.10068128,  0.00118084,  0.00055984, -0.00811506,
       -0.19697376,  0.01185539,  0.00096166])
2025-08-08 23:12:30 - shap - INFO - num_full_subsets = 4
2025-08-08 23:12:30 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-08-08 23:12:30 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-08-08 23:12:30 - shap - INFO - phi = array([ 0.00258162, -0.06502122,  0.00151731, -0.00058096,  0.01352281,
       -0.37256672, -0.00464105,  0.00056336])
2025-08-08 23:12:30 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-08-08 23:12:30 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-08-08 23:12:30 - shap - INFO - phi = array([-0.00258162,  0.06502122, -0.00151731,  0.00058096, -0.01352281,
        0.37256672,  0.00464105, -0.00056336])
2025-08-08 23:12:30 - shap - INFO - num_full_subsets = 4
2025-08-08 23:12:30 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-08-08 23:12:30 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-08-08 23:12:30 - shap - INFO - phi = array([ 0.00254604, -0.09087872,  0.00143338, -0.00058779,  0.01047632,
       -0.35498706,  0.00611991,  0.00122363])
2025-08-08 23:12:30 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-08-08 23:12:30 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-08-08 23:12:30 - shap - INFO - phi = array([-0.00254604,  0.09087872, -0.00143338,  0.00058779, -0.01047632,
        0.35498706, -0.00611991, -0.00122363])
2025-08-08 23:12:30 - shap - INFO - num_full_subsets = 4
2025-08-08 23:12:30 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-08-08 23:12:30 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-08-08 23:12:30 - shap - INFO - phi = array([-3.69350627e-03,  7.55824991e-02, -1.29535331e-03, -6.34124796e-04,
        9.66551390e-03,  1.93898257e-01, -1.07144293e-02,  1.15321782e-04])
2025-08-08 23:12:30 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-08-08 23:12:30 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-08-08 23:12:30 - shap - INFO - phi = array([ 3.69350627e-03, -7.55824991e-02,  1.29535331e-03,  6.34124796e-04,
       -9.66551390e-03, -1.93898257e-01,  1.07144293e-02, -1.15321782e-04])
2025-08-08 23:12:30 - shap - INFO - num_full_subsets = 4
2025-08-08 23:12:31 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-08-08 23:12:31 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-08-08 23:12:31 - shap - INFO - phi = array([-0.00335065, -0.02070703,  0.00127806, -0.00057725, -0.00648102,
       -0.36980926, -0.03119053,  0.00122509])
2025-08-08 23:12:31 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-08-08 23:12:31 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-08-08 23:12:31 - shap - INFO - phi = array([ 0.00335065,  0.02070703, -0.00127806,  0.00057725,  0.00648102,
        0.36980926,  0.03119053, -0.00122509])
2025-08-08 23:12:31 - shap - INFO - num_full_subsets = 4
2025-08-08 23:12:31 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-08-08 23:12:31 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-08-08 23:12:31 - shap - INFO - phi = array([ 0.00251305, -0.05807694,  0.00147911, -0.00056926,  0.02876334,
       -0.40151912,  0.00197198,  0.000813  ])
2025-08-08 23:12:31 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-08-08 23:12:31 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-08-08 23:12:31 - shap - INFO - phi = array([-0.00251305,  0.05807694, -0.00147911,  0.00056926, -0.02876334,
        0.40151912, -0.00197198, -0.000813  ])
2025-08-08 23:12:31 - shap - INFO - num_full_subsets = 4
2025-08-08 23:12:31 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-08-08 23:12:31 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-08-08 23:12:31 - shap - INFO - phi = array([-0.00354414,  0.07402153, -0.00138236, -0.00066412,  0.00322691,
        0.19944925, -0.00564153, -0.00251806])
2025-08-08 23:12:31 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-08-08 23:12:31 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-08-08 23:12:31 - shap - INFO - phi = array([ 0.00354414, -0.07402153,  0.00138236,  0.00066412, -0.00322691,
       -0.19944925,  0.00564153,  0.00251806])
2025-08-08 23:12:31 - shap - INFO - num_full_subsets = 4
2025-08-08 23:12:31 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-08-08 23:12:31 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-08-08 23:12:31 - shap - INFO - phi = array([ 2.55974045e-03, -6.74479489e-02,  1.33079999e-03, -3.68735739e-04,
        1.44098403e-02, -3.68389665e-01, -6.71000905e-03, -1.54087352e-04])
2025-08-08 23:12:31 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-08-08 23:12:31 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-08-08 23:12:31 - shap - INFO - phi = array([-2.55974045e-03,  6.74479489e-02, -1.33079999e-03,  3.68735739e-04,
       -1.44098403e-02,  3.68389665e-01,  6.71000905e-03,  1.54087352e-04])
2025-08-08 23:12:31 - shap - INFO - num_full_subsets = 4
2025-08-08 23:12:31 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-08-08 23:12:31 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-08-08 23:12:31 - shap - INFO - phi = array([ 0.00318455, -0.07040097, -0.00097587, -0.00054019,  0.03558482,
       -0.31637325, -0.07530873, -0.00321361])
2025-08-08 23:12:31 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-08-08 23:12:31 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-08-08 23:12:31 - shap - INFO - phi = array([-0.00318455,  0.07040097,  0.00097587,  0.00054019, -0.03558482,
        0.31637325,  0.07530873,  0.00321361])
2025-08-08 23:12:31 - shap - INFO - num_full_subsets = 4
2025-08-08 23:12:31 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-08-08 23:12:31 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-08-08 23:12:31 - shap - INFO - phi = array([ 3.45915094e-03, -2.29204348e-02, -5.28666266e-04, -6.86291930e-04,
       -1.06374682e-02,  2.65367386e-01,  5.93578127e-02, -3.35482377e-05])
2025-08-08 23:12:31 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-08-08 23:12:31 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-08-08 23:12:31 - shap - INFO - phi = array([-3.45915094e-03,  2.29204348e-02,  5.28666266e-04,  6.86291930e-04,
        1.06374682e-02, -2.65367386e-01, -5.93578127e-02,  3.35482377e-05])
2025-08-08 23:12:31 - shap - INFO - num_full_subsets = 4
2025-08-08 23:12:31 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-08-08 23:12:31 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-08-08 23:12:31 - shap - INFO - phi = array([-0.00336725,  0.04514861, -0.00101466,  0.00041902, -0.01359994,
        0.25441406,  0.00878142, -0.00204826])
2025-08-08 23:12:31 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-08-08 23:12:31 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-08-08 23:12:31 - shap - INFO - phi = array([ 0.00336725, -0.04514861,  0.00101466, -0.00041902,  0.01359994,
       -0.25441406, -0.00878142,  0.00204826])
2025-08-08 23:12:31 - shap - INFO - num_full_subsets = 4
2025-08-08 23:12:31 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-08-08 23:12:31 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-08-08 23:12:31 - shap - INFO - phi = array([-0.00358659,  0.07266954, -0.00138879, -0.00063551,  0.00676017,
        0.19684406, -0.00780013,  0.        ])
2025-08-08 23:12:31 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-08-08 23:12:31 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-08-08 23:12:31 - shap - INFO - phi = array([ 0.00358659, -0.07266954,  0.00138879,  0.00063551, -0.00676017,
       -0.19684406,  0.00780013,  0.        ])
2025-08-08 23:12:31 - shap - INFO - num_full_subsets = 4
2025-08-08 23:12:32 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-08-08 23:12:32 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-08-08 23:12:32 - shap - INFO - phi = array([ 0.00253353, -0.0587882 , -0.00146799, -0.00054242,  0.0310127 ,
       -0.39940151, -0.00452062,  0.0025657 ])
2025-08-08 23:12:32 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-08-08 23:12:32 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-08-08 23:12:32 - shap - INFO - phi = array([-0.00253353,  0.0587882 ,  0.00146799,  0.00054242, -0.0310127 ,
        0.39940151,  0.00452062, -0.0025657 ])
2025-08-08 23:12:32 - shap - INFO - num_full_subsets = 4
2025-08-08 23:12:32 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-08-08 23:12:32 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-08-08 23:12:32 - shap - INFO - phi = array([ 0.00250625, -0.06021183,  0.00144635, -0.00056976,  0.02814118,
       -0.39594703, -0.00086015,  0.00087015])
2025-08-08 23:12:32 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-08-08 23:12:32 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-08-08 23:12:32 - shap - INFO - phi = array([-0.00250625,  0.06021183, -0.00144635,  0.00056976, -0.02814118,
        0.39594703,  0.00086015, -0.00087015])
2025-08-08 23:12:32 - shap - INFO - num_full_subsets = 4
2025-08-08 23:12:32 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-08-08 23:12:32 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-08-08 23:12:32 - shap - INFO - phi = array([ 3.06493266e-03,  6.89295822e-02, -1.62840440e-03, -2.23378894e-04,
        1.31243442e-01, -2.54533631e-01,  1.56138824e-02,  1.19691334e-02])
2025-08-08 23:12:32 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-08-08 23:12:32 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-08-08 23:12:32 - shap - INFO - phi = array([-3.06493266e-03, -6.89295822e-02,  1.62840440e-03,  2.23378894e-04,
       -1.31243442e-01,  2.54533631e-01, -1.56138824e-02, -1.19691334e-02])
2025-08-08 23:12:32 - shap - INFO - num_full_subsets = 4
2025-08-08 23:12:32 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-08-08 23:12:32 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-08-08 23:12:32 - shap - INFO - phi = array([-0.00379588,  0.10052076,  0.00052218,  0.00465658, -0.03715842,
        0.1724001 ,  0.02983045,  0.00084832])
2025-08-08 23:12:32 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-08-08 23:12:32 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-08-08 23:12:32 - shap - INFO - phi = array([ 0.00379588, -0.10052076, -0.00052218, -0.00465658,  0.03715842,
       -0.1724001 , -0.02983045, -0.00084832])
2025-08-08 23:12:32 - shap - INFO - num_full_subsets = 4
2025-08-08 23:12:32 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-08-08 23:12:32 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-08-08 23:12:32 - shap - INFO - phi = array([-0.00432058,  0.11657018, -0.0007012 , -0.0004853 ,  0.02979143,
        0.18417741, -0.03684473,  0.00029537])
2025-08-08 23:12:32 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-08-08 23:12:32 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-08-08 23:12:32 - shap - INFO - phi = array([ 0.00432058, -0.11657018,  0.0007012 ,  0.0004853 , -0.02979143,
       -0.18417741,  0.03684473, -0.00029537])
2025-08-08 23:12:32 - shap - INFO - num_full_subsets = 4
2025-08-08 23:12:32 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-08-08 23:12:32 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-08-08 23:12:32 - shap - INFO - phi = array([ 0.00255356,  0.02114529, -0.00056619,  0.00077099, -0.01879664,
        0.27253385,  0.01772256, -0.00125847])
2025-08-08 23:12:32 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-08-08 23:12:32 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-08-08 23:12:32 - shap - INFO - phi = array([-0.00255356, -0.02114529,  0.00056619, -0.00077099,  0.01879664,
       -0.27253385, -0.01772256,  0.00125847])
2025-08-08 23:12:32 - shap - INFO - num_full_subsets = 4
2025-08-08 23:12:32 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-08-08 23:12:32 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-08-08 23:12:32 - shap - INFO - phi = array([-0.00342301,  0.00830713, -0.00109591, -0.00054766,  0.0057214 ,
        0.29673714, -0.0135543 , -0.00422261])
2025-08-08 23:12:32 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-08-08 23:12:32 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-08-08 23:12:32 - shap - INFO - phi = array([ 0.00342301, -0.00830713,  0.00109591,  0.00054766, -0.0057214 ,
       -0.29673714,  0.0135543 ,  0.00422261])
2025-08-08 23:12:32 - shap - INFO - num_full_subsets = 4
2025-08-08 23:12:33 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-08-08 23:12:33 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-08-08 23:12:33 - shap - INFO - phi = array([ 2.69082860e-03,  5.64646502e-02,  5.46971870e-05,  1.32857110e-03,
       -3.12566135e-02,  2.30100335e-01,  1.30596302e-02,  2.41736494e-04])
2025-08-08 23:12:33 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-08-08 23:12:33 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-08-08 23:12:33 - shap - INFO - phi = array([-2.69082860e-03, -5.64646502e-02, -5.46971870e-05, -1.32857110e-03,
        3.12566135e-02, -2.30100335e-01, -1.30596302e-02, -2.41736494e-04])
2025-08-08 23:12:33 - shap - INFO - num_full_subsets = 4
2025-08-08 23:12:33 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-08-08 23:12:33 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-08-08 23:12:33 - shap - INFO - phi = array([ 0.00249836, -0.06352135,  0.00182114, -0.00039611,  0.02646543,
       -0.39046963, -0.00145504,  0.00083001])
2025-08-08 23:12:33 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-08-08 23:12:33 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-08-08 23:12:33 - shap - INFO - phi = array([-0.00249836,  0.06352135, -0.00182114,  0.00039611, -0.02646543,
        0.39046963,  0.00145504, -0.00083001])
2025-08-08 23:12:33 - shap - INFO - num_full_subsets = 4
2025-08-08 23:12:33 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-08-08 23:12:33 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-08-08 23:12:33 - shap - INFO - phi = array([ 0.00272502, -0.00520394,  0.00037944, -0.00049485, -0.00166938,
        0.29634339,  0.00482744, -0.00342351])
2025-08-08 23:12:33 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-08-08 23:12:33 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-08-08 23:12:33 - shap - INFO - phi = array([-0.00272502,  0.00520394, -0.00037944,  0.00049485,  0.00166938,
       -0.29634339, -0.00482744,  0.00342351])
2025-08-08 23:12:33 - shap - INFO - num_full_subsets = 4
2025-08-08 23:12:33 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-08-08 23:12:33 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-08-08 23:12:33 - shap - INFO - phi = array([-0.00323675, -0.06083584,  0.00124674, -0.00057458,  0.01988388,
       -0.36980984, -0.01822547,  0.00193927])
2025-08-08 23:12:33 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-08-08 23:12:33 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-08-08 23:12:33 - shap - INFO - phi = array([ 0.00323675,  0.06083584, -0.00124674,  0.00057458, -0.01988388,
        0.36980984,  0.01822547, -0.00193927])
2025-08-08 23:12:33 - shap - INFO - num_full_subsets = 4
2025-08-08 23:12:33 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-08-08 23:12:33 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-08-08 23:12:33 - shap - INFO - phi = array([ 0.00250201, -0.06015943,  0.00141938, -0.00056431,  0.02799521,
       -0.39946957,  0.00233765,  0.00128477])
2025-08-08 23:12:33 - shap - INFO - np.sum(w_aug) = 8.000000000000002
2025-08-08 23:12:33 - shap - INFO - np.sum(self.kernelWeights) = 0.9999999999999996
2025-08-08 23:12:33 - shap - INFO - phi = array([-0.00250201,  0.06015943, -0.00141938,  0.00056431, -0.02799521,
        0.39946957, -0.00233765, -0.00128477])
