{"session_id": "20250808_230919", "session_name": "训练_nodule2_20250808_230919", "description": "自动创建的训练会话，基于数据文件: nodule2", "created_time": "2025-08-08T23:09:19.549218", "last_modified": "2025-08-08T23:12:20.851913", "trained_models": [{"model_name": "DecisionTree", "model_type": "single", "filename": "DecisionTree_single_230919.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250808_230919\\models\\DecisionTree_single_230919.joblib", "save_time": "2025-08-08T23:09:19.595964"}, {"model_name": "RandomForest", "model_type": "single", "filename": "RandomForest_single_230919.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250808_230919\\models\\RandomForest_single_230919.joblib", "save_time": "2025-08-08T23:09:19.704719"}, {"model_name": "XGBoost", "model_type": "single", "filename": "XGBoost_single_230919.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250808_230919\\models\\XGBoost_single_230919.joblib", "save_time": "2025-08-08T23:09:19.809085"}, {"model_name": "LightGBM", "model_type": "single", "filename": "LightGBM_single_230919.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250808_230919\\models\\LightGBM_single_230919.joblib", "save_time": "2025-08-08T23:09:19.877228"}, {"model_name": "CatBoost", "model_type": "single", "filename": "CatBoost_single_230920.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250808_230919\\models\\CatBoost_single_230920.joblib", "save_time": "2025-08-08T23:09:20.937172"}, {"model_name": "Logistic", "model_type": "single", "filename": "Logistic_single_230920.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250808_230919\\models\\Logistic_single_230920.joblib", "save_time": "2025-08-08T23:09:20.963103"}, {"model_name": "SVM", "model_type": "single", "filename": "SVM_single_230920.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250808_230919\\models\\SVM_single_230920.joblib", "save_time": "2025-08-08T23:09:20.994537"}, {"model_name": "KNN", "model_type": "single", "filename": "KNN_single_230921.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250808_230919\\models\\KNN_single_230921.joblib", "save_time": "2025-08-08T23:09:21.026596"}, {"model_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "model_type": "single", "filename": "NaiveBayes_single_230921.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250808_230919\\models\\NaiveBayes_single_230921.joblib", "save_time": "2025-08-08T23:09:21.058402"}, {"model_name": "NeuralNet", "model_type": "single", "filename": "NeuralNet_single_230921.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250808_230919\\models\\NeuralNet_single_230921.joblib", "save_time": "2025-08-08T23:09:21.326960"}, {"model_name": "DecisionTree", "model_type": "single", "filename": "DecisionTree_single_231211.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250808_230919\\models\\DecisionTree_single_231211.joblib", "save_time": "2025-08-08T23:12:11.457566"}, {"model_name": "RandomForest", "model_type": "single", "filename": "RandomForest_single_231211.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250808_230919\\models\\RandomForest_single_231211.joblib", "save_time": "2025-08-08T23:12:11.546546"}, {"model_name": "XGBoost", "model_type": "single", "filename": "XGBoost_single_231211.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250808_230919\\models\\XGBoost_single_231211.joblib", "save_time": "2025-08-08T23:12:11.637948"}, {"model_name": "LightGBM", "model_type": "single", "filename": "LightGBM_single_231211.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250808_230919\\models\\LightGBM_single_231211.joblib", "save_time": "2025-08-08T23:12:11.688806"}, {"model_name": "CatBoost", "model_type": "single", "filename": "CatBoost_single_231212.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250808_230919\\models\\CatBoost_single_231212.joblib", "save_time": "2025-08-08T23:12:12.743918"}, {"model_name": "Logistic", "model_type": "single", "filename": "Logistic_single_231212.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250808_230919\\models\\Logistic_single_231212.joblib", "save_time": "2025-08-08T23:12:12.759418"}, {"model_name": "SVM", "model_type": "single", "filename": "SVM_single_231212.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250808_230919\\models\\SVM_single_231212.joblib", "save_time": "2025-08-08T23:12:12.785308"}, {"model_name": "KNN", "model_type": "single", "filename": "KNN_single_231212.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250808_230919\\models\\KNN_single_231212.joblib", "save_time": "2025-08-08T23:12:12.804771"}, {"model_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "model_type": "single", "filename": "NaiveBayes_single_231212.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250808_230919\\models\\NaiveBayes_single_231212.joblib", "save_time": "2025-08-08T23:12:12.825380"}, {"model_name": "NeuralNet", "model_type": "single", "filename": "NeuralNet_single_231213.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250808_230919\\models\\NeuralNet_single_231213.joblib", "save_time": "2025-08-08T23:12:13.071339"}, {"model_name": "ensemble_20250808_231220", "model_type": "ensemble", "filename": "ensemble_20250808_231220_ensemble_231220.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250808_230919\\models\\ensemble_20250808_231220_ensemble_231220.joblib", "save_time": "2025-08-08T23:12:20.772671"}], "ensemble_results": [], "data_files": [], "plots": [], "logs": [], "status": "created"}