2025-08-08 18:38:50 - model_training - INFO - 模型名称: Random Forest
2025-08-08 18:38:50 - model_training - INFO - 准确率: 0.7000
2025-08-08 18:38:50 - model_training - INFO - AUC: 0.8313
2025-08-08 18:38:50 - model_training - INFO - AUPRC: 0.8569
2025-08-08 18:38:50 - model_training - INFO - 混淆矩阵:
2025-08-08 18:38:50 - model_training - INFO - 
[[13  7]
 [ 5 15]]
2025-08-08 18:38:50 - model_training - INFO - 
分类报告:
2025-08-08 18:38:50 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.72      0.65      0.68        20
           1       0.68      0.75      0.71        20

    accuracy                           0.70        40
   macro avg       0.70      0.70      0.70        40
weighted avg       0.70      0.70      0.70        40

2025-08-08 18:38:50 - model_training - INFO - 训练时间: 0.25 秒
2025-08-08 18:38:50 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.7000
2025-08-08 18:38:50 - model_training - INFO - 模型 RandomForest 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-08-08 18:38:50 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_feature_names.joblib
2025-08-08 18:45:41 - model_training - INFO - 模型名称: Random Forest
2025-08-08 18:45:41 - model_training - INFO - 准确率: 0.8750
2025-08-08 18:45:41 - model_training - INFO - AUC: 0.9412
2025-08-08 18:45:41 - model_training - INFO - AUPRC: 0.9359
2025-08-08 18:45:41 - model_training - INFO - 混淆矩阵:
2025-08-08 18:45:41 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-08-08 18:45:41 - model_training - INFO - 
分类报告:
2025-08-08 18:45:41 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-08 18:45:41 - model_training - INFO - 训练时间: 0.09 秒
2025-08-08 18:45:41 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.8750
2025-08-08 18:45:41 - model_training - INFO - 模型 RandomForest 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_184541\models\RandomForest_single_184541.joblib
2025-08-08 18:45:41 - model_training - INFO - 模型 RandomForest 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-08-08 18:45:41 - model_training - INFO - 模型名称: SVM
2025-08-08 18:45:41 - model_training - INFO - 准确率: 0.7750
2025-08-08 18:45:41 - model_training - INFO - AUC: 0.9182
2025-08-08 18:45:41 - model_training - INFO - AUPRC: 0.9034
2025-08-08 18:45:41 - model_training - INFO - 混淆矩阵:
2025-08-08 18:45:41 - model_training - INFO - 
[[19  4]
 [ 5 12]]
2025-08-08 18:45:41 - model_training - INFO - 
分类报告:
2025-08-08 18:45:41 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.79      0.83      0.81        23
           1       0.75      0.71      0.73        17

    accuracy                           0.78        40
   macro avg       0.77      0.77      0.77        40
weighted avg       0.77      0.78      0.77        40

2025-08-08 18:45:41 - model_training - INFO - 训练时间: 0.01 秒
2025-08-08 18:45:41 - model_training - INFO - 模型 SVM 性能: 准确率=0.7750
2025-08-08 18:45:41 - model_training - INFO - 模型 SVM 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_184541\models\SVM_single_184541.joblib
2025-08-08 18:45:41 - model_training - INFO - 模型 SVM 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\SVM_results.joblib
2025-08-08 19:01:34 - model_training - INFO - 模型名称: Random Forest
2025-08-08 19:01:34 - model_training - INFO - 准确率: 0.8750
2025-08-08 19:01:34 - model_training - INFO - AUC: 0.9412
2025-08-08 19:01:34 - model_training - INFO - AUPRC: 0.9359
2025-08-08 19:01:34 - model_training - INFO - 混淆矩阵:
2025-08-08 19:01:34 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-08-08 19:01:34 - model_training - INFO - 
分类报告:
2025-08-08 19:01:34 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-08 19:01:34 - model_training - INFO - 训练时间: 0.09 秒
2025-08-08 19:01:34 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.8750
2025-08-08 19:01:34 - model_training - INFO - 模型 RandomForest 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_190134\models\RandomForest_single_190134.joblib
2025-08-08 19:01:34 - model_training - INFO - 模型 RandomForest 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-08-08 20:16:53 - model_training - INFO - 模型名称: Logistic Regression
2025-08-08 20:16:53 - model_training - INFO - 准确率: 0.5500
2025-08-08 20:16:53 - model_training - INFO - AUC: 0.6125
2025-08-08 20:16:53 - model_training - INFO - AUPRC: 0.4726
2025-08-08 20:16:53 - model_training - INFO - 混淆矩阵:
2025-08-08 20:16:53 - model_training - INFO - 
[[12 15]
 [ 3 10]]
2025-08-08 20:16:53 - model_training - INFO - 
分类报告:
2025-08-08 20:16:53 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.80      0.44      0.57        27
           1       0.40      0.77      0.53        13

    accuracy                           0.55        40
   macro avg       0.60      0.61      0.55        40
weighted avg       0.67      0.55      0.56        40

2025-08-08 20:16:53 - model_training - INFO - 训练时间: 0.01 秒
2025-08-08 20:16:53 - model_training - INFO - 模型 Logistic 性能: 准确率=0.5500
2025-08-08 20:16:53 - model_training - INFO - 模型 Logistic 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_results.joblib
2025-08-08 20:16:53 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_feature_names.joblib
2025-08-08 20:17:21 - model_training - INFO - 模型名称: Logistic Regression
2025-08-08 20:17:21 - model_training - INFO - 准确率: 0.5500
2025-08-08 20:17:21 - model_training - INFO - AUC: 0.6125
2025-08-08 20:17:21 - model_training - INFO - AUPRC: 0.4726
2025-08-08 20:17:21 - model_training - INFO - 混淆矩阵:
2025-08-08 20:17:21 - model_training - INFO - 
[[12 15]
 [ 3 10]]
2025-08-08 20:17:21 - model_training - INFO - 
分类报告:
2025-08-08 20:17:21 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.80      0.44      0.57        27
           1       0.40      0.77      0.53        13

    accuracy                           0.55        40
   macro avg       0.60      0.61      0.55        40
weighted avg       0.67      0.55      0.56        40

2025-08-08 20:17:21 - model_training - INFO - 训练时间: 0.01 秒
2025-08-08 20:17:21 - model_training - INFO - 模型 Logistic 性能: 准确率=0.5500
2025-08-08 20:17:21 - model_training - INFO - 模型 Logistic 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_results.joblib
2025-08-08 20:17:21 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_feature_names.joblib
2025-08-08 21:19:24 - model_training - INFO - 模型名称: Random Forest
2025-08-08 21:19:24 - model_training - INFO - 准确率: 0.8780
2025-08-08 21:19:24 - model_training - INFO - AUC: 0.9481
2025-08-08 21:19:24 - model_training - INFO - AUPRC: 0.9453
2025-08-08 21:19:24 - model_training - INFO - 混淆矩阵:
2025-08-08 21:19:24 - model_training - INFO - 
[[21  2]
 [ 3 15]]
2025-08-08 21:19:24 - model_training - INFO - 
分类报告:
2025-08-08 21:19:24 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.83      0.86        18

    accuracy                           0.88        41
   macro avg       0.88      0.87      0.88        41
weighted avg       0.88      0.88      0.88        41

2025-08-08 21:19:24 - model_training - INFO - 训练时间: 0.08 秒
2025-08-08 21:19:24 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.8780
2025-08-08 21:19:24 - model_training - INFO - 模型 RandomForest 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_211924\models\RandomForest_single_211924.joblib
2025-08-08 21:19:24 - model_training - INFO - 模型 RandomForest 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-08-08 21:27:50 - model_training - INFO - 模型名称: Random Forest
2025-08-08 21:27:50 - model_training - INFO - 准确率: 0.7000
2025-08-08 21:27:50 - model_training - INFO - AUC: 0.8313
2025-08-08 21:27:50 - model_training - INFO - AUPRC: 0.8569
2025-08-08 21:27:50 - model_training - INFO - 混淆矩阵:
2025-08-08 21:27:50 - model_training - INFO - 
[[13  7]
 [ 5 15]]
2025-08-08 21:27:50 - model_training - INFO - 
分类报告:
2025-08-08 21:27:50 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.72      0.65      0.68        20
           1       0.68      0.75      0.71        20

    accuracy                           0.70        40
   macro avg       0.70      0.70      0.70        40
weighted avg       0.70      0.70      0.70        40

2025-08-08 21:27:50 - model_training - INFO - 训练时间: 0.25 秒
2025-08-08 21:27:50 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.7000
2025-08-08 21:27:50 - model_training - INFO - 模型 RandomForest 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-08-08 21:27:50 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_feature_names.joblib
2025-08-08 21:57:18 - model_training - INFO - 模型名称: Random Forest
2025-08-08 21:57:18 - model_training - INFO - 准确率: 0.8750
2025-08-08 21:57:18 - model_training - INFO - AUC: 0.9412
2025-08-08 21:57:18 - model_training - INFO - AUPRC: 0.9359
2025-08-08 21:57:18 - model_training - INFO - 混淆矩阵:
2025-08-08 21:57:18 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-08-08 21:57:18 - model_training - INFO - 
分类报告:
2025-08-08 21:57:18 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-08 21:57:18 - model_training - INFO - 训练时间: 0.08 秒
2025-08-08 21:57:18 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.8750
2025-08-08 21:57:18 - model_training - INFO - 模型 RandomForest 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_215718\models\RandomForest_single_215718.joblib
2025-08-08 21:57:18 - model_training - INFO - 模型 RandomForest 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-08-08 23:09:19 - model_training - INFO - 模型名称: Decision Tree
2025-08-08 23:09:19 - model_training - INFO - 准确率: 0.8000
2025-08-08 23:09:19 - model_training - INFO - AUC: 0.8951
2025-08-08 23:09:19 - model_training - INFO - AUPRC: 0.7948
2025-08-08 23:09:19 - model_training - INFO - 混淆矩阵:
2025-08-08 23:09:19 - model_training - INFO - 
[[20  3]
 [ 5 12]]
2025-08-08 23:09:19 - model_training - INFO - 
分类报告:
2025-08-08 23:09:19 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.80      0.87      0.83        23
           1       0.80      0.71      0.75        17

    accuracy                           0.80        40
   macro avg       0.80      0.79      0.79        40
weighted avg       0.80      0.80      0.80        40

2025-08-08 23:09:19 - model_training - INFO - 训练时间: 0.01 秒
2025-08-08 23:09:19 - model_training - INFO - 模型 DecisionTree 性能: 准确率=0.8000
2025-08-08 23:09:19 - model_training - INFO - 模型 DecisionTree 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\DecisionTree_single_230919.joblib
2025-08-08 23:09:19 - model_training - INFO - 模型 DecisionTree 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\DecisionTree_results.joblib
2025-08-08 23:09:19 - model_training - INFO - 模型名称: Random Forest
2025-08-08 23:09:19 - model_training - INFO - 准确率: 0.8750
2025-08-08 23:09:19 - model_training - INFO - AUC: 0.9412
2025-08-08 23:09:19 - model_training - INFO - AUPRC: 0.9359
2025-08-08 23:09:19 - model_training - INFO - 混淆矩阵:
2025-08-08 23:09:19 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-08-08 23:09:19 - model_training - INFO - 
分类报告:
2025-08-08 23:09:19 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-08 23:09:19 - model_training - INFO - 训练时间: 0.07 秒
2025-08-08 23:09:19 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.8750
2025-08-08 23:09:19 - model_training - INFO - 模型 RandomForest 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\RandomForest_single_230919.joblib
2025-08-08 23:09:19 - model_training - INFO - 模型 RandomForest 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-08-08 23:09:19 - model_training - INFO - [XGBoost] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-08 23:09:19 - model_training - INFO - 模型名称: XGBoost
2025-08-08 23:09:19 - model_training - INFO - 准确率: 0.9000
2025-08-08 23:09:19 - model_training - INFO - AUC: 0.9719
2025-08-08 23:09:19 - model_training - INFO - AUPRC: 0.9627
2025-08-08 23:09:19 - model_training - INFO - 混淆矩阵:
2025-08-08 23:09:19 - model_training - INFO - 
[[21  2]
 [ 2 15]]
2025-08-08 23:09:19 - model_training - INFO - 
分类报告:
2025-08-08 23:09:19 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.91      0.91      0.91        23
           1       0.88      0.88      0.88        17

    accuracy                           0.90        40
   macro avg       0.90      0.90      0.90        40
weighted avg       0.90      0.90      0.90        40

2025-08-08 23:09:19 - model_training - INFO - 训练时间: 0.05 秒
2025-08-08 23:09:19 - model_training - INFO - 模型 XGBoost 性能: 准确率=0.9000
2025-08-08 23:09:19 - model_training - INFO - 模型 XGBoost 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\XGBoost_single_230919.joblib
2025-08-08 23:09:19 - model_training - INFO - 模型 XGBoost 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_results.joblib
2025-08-08 23:09:19 - model_training - INFO - [LightGBM] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-08 23:09:19 - model_training - INFO - 模型名称: LightGBM
2025-08-08 23:09:19 - model_training - INFO - 准确率: 0.8500
2025-08-08 23:09:19 - model_training - INFO - AUC: 0.9488
2025-08-08 23:09:19 - model_training - INFO - AUPRC: 0.9492
2025-08-08 23:09:19 - model_training - INFO - 混淆矩阵:
2025-08-08 23:09:19 - model_training - INFO - 
[[20  3]
 [ 3 14]]
2025-08-08 23:09:19 - model_training - INFO - 
分类报告:
2025-08-08 23:09:19 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.87      0.87      0.87        23
           1       0.82      0.82      0.82        17

    accuracy                           0.85        40
   macro avg       0.85      0.85      0.85        40
weighted avg       0.85      0.85      0.85        40

2025-08-08 23:09:19 - model_training - INFO - 训练时间: 0.03 秒
2025-08-08 23:09:19 - model_training - INFO - 模型 LightGBM 性能: 准确率=0.8500
2025-08-08 23:09:19 - model_training - INFO - 模型 LightGBM 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\LightGBM_single_230919.joblib
2025-08-08 23:09:19 - model_training - INFO - 模型 LightGBM 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_results.joblib
2025-08-08 23:09:19 - model_training - INFO - [CatBoost] 启用 auto_class_weights='Balanced' 以处理不平衡
2025-08-08 23:09:20 - model_training - INFO - 模型名称: CatBoost
2025-08-08 23:09:20 - model_training - INFO - 准确率: 0.8750
2025-08-08 23:09:20 - model_training - INFO - AUC: 0.9591
2025-08-08 23:09:20 - model_training - INFO - AUPRC: 0.9570
2025-08-08 23:09:20 - model_training - INFO - 混淆矩阵:
2025-08-08 23:09:20 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-08-08 23:09:20 - model_training - INFO - 
分类报告:
2025-08-08 23:09:20 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-08 23:09:20 - model_training - INFO - 训练时间: 1.02 秒
2025-08-08 23:09:20 - model_training - INFO - 模型 CatBoost 性能: 准确率=0.8750
2025-08-08 23:09:20 - model_training - INFO - 模型 CatBoost 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\CatBoost_single_230920.joblib
2025-08-08 23:09:20 - model_training - INFO - 模型 CatBoost 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\CatBoost_results.joblib
2025-08-08 23:09:20 - model_training - INFO - 模型名称: Logistic Regression
2025-08-08 23:09:20 - model_training - INFO - 准确率: 0.8250
2025-08-08 23:09:20 - model_training - INFO - AUC: 0.9284
2025-08-08 23:09:20 - model_training - INFO - AUPRC: 0.9288
2025-08-08 23:09:20 - model_training - INFO - 混淆矩阵:
2025-08-08 23:09:20 - model_training - INFO - 
[[20  3]
 [ 4 13]]
2025-08-08 23:09:20 - model_training - INFO - 
分类报告:
2025-08-08 23:09:20 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.83      0.87      0.85        23
           1       0.81      0.76      0.79        17

    accuracy                           0.82        40
   macro avg       0.82      0.82      0.82        40
weighted avg       0.82      0.82      0.82        40

2025-08-08 23:09:20 - model_training - INFO - 训练时间: 0.02 秒
2025-08-08 23:09:20 - model_training - INFO - 模型 Logistic 性能: 准确率=0.8250
2025-08-08 23:09:20 - model_training - INFO - 模型 Logistic 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\Logistic_single_230920.joblib
2025-08-08 23:09:20 - model_training - INFO - 模型 Logistic 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_results.joblib
2025-08-08 23:09:20 - model_training - INFO - 模型名称: SVM
2025-08-08 23:09:20 - model_training - INFO - 准确率: 0.7750
2025-08-08 23:09:20 - model_training - INFO - AUC: 0.9182
2025-08-08 23:09:20 - model_training - INFO - AUPRC: 0.9034
2025-08-08 23:09:20 - model_training - INFO - 混淆矩阵:
2025-08-08 23:09:20 - model_training - INFO - 
[[19  4]
 [ 5 12]]
2025-08-08 23:09:20 - model_training - INFO - 
分类报告:
2025-08-08 23:09:20 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.79      0.83      0.81        23
           1       0.75      0.71      0.73        17

    accuracy                           0.78        40
   macro avg       0.77      0.77      0.77        40
weighted avg       0.77      0.78      0.77        40

2025-08-08 23:09:20 - model_training - INFO - 训练时间: 0.02 秒
2025-08-08 23:09:20 - model_training - INFO - 模型 SVM 性能: 准确率=0.7750
2025-08-08 23:09:20 - model_training - INFO - 模型 SVM 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\SVM_single_230920.joblib
2025-08-08 23:09:21 - model_training - INFO - 模型 SVM 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\SVM_results.joblib
2025-08-08 23:09:21 - model_training - INFO - 模型名称: KNN
2025-08-08 23:09:21 - model_training - INFO - 准确率: 0.8750
2025-08-08 23:09:21 - model_training - INFO - AUC: 0.9322
2025-08-08 23:09:21 - model_training - INFO - AUPRC: 0.9189
2025-08-08 23:09:21 - model_training - INFO - 混淆矩阵:
2025-08-08 23:09:21 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-08-08 23:09:21 - model_training - INFO - 
分类报告:
2025-08-08 23:09:21 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-08 23:09:21 - model_training - INFO - 训练时间: 0.02 秒
2025-08-08 23:09:21 - model_training - INFO - 模型 KNN 性能: 准确率=0.8750
2025-08-08 23:09:21 - model_training - INFO - 模型 KNN 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\KNN_single_230921.joblib
2025-08-08 23:09:21 - model_training - INFO - 模型 KNN 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\KNN_results.joblib
2025-08-08 23:09:21 - model_training - INFO - 模型名称: Naive Bayes
2025-08-08 23:09:21 - model_training - INFO - 准确率: 0.8750
2025-08-08 23:09:21 - model_training - INFO - AUC: 0.8977
2025-08-08 23:09:21 - model_training - INFO - AUPRC: 0.9096
2025-08-08 23:09:21 - model_training - INFO - 混淆矩阵:
2025-08-08 23:09:21 - model_training - INFO - 
[[22  1]
 [ 4 13]]
2025-08-08 23:09:21 - model_training - INFO - 
分类报告:
2025-08-08 23:09:21 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.85      0.96      0.90        23
           1       0.93      0.76      0.84        17

    accuracy                           0.88        40
   macro avg       0.89      0.86      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-08 23:09:21 - model_training - INFO - 训练时间: 0.00 秒
2025-08-08 23:09:21 - model_training - INFO - 模型 NaiveBayes 性能: 准确率=0.8750
2025-08-08 23:09:21 - model_training - INFO - 模型 NaiveBayes 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\NaiveBayes_single_230921.joblib
2025-08-08 23:09:21 - model_training - INFO - 模型 NaiveBayes 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NaiveBayes_results.joblib
2025-08-08 23:09:21 - model_training - INFO - 模型名称: Neural Network
2025-08-08 23:09:21 - model_training - INFO - 准确率: 0.8750
2025-08-08 23:09:21 - model_training - INFO - AUC: 0.9591
2025-08-08 23:09:21 - model_training - INFO - AUPRC: 0.9450
2025-08-08 23:09:21 - model_training - INFO - 混淆矩阵:
2025-08-08 23:09:21 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-08-08 23:09:21 - model_training - INFO - 
分类报告:
2025-08-08 23:09:21 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-08 23:09:21 - model_training - INFO - 训练时间: 0.24 秒
2025-08-08 23:09:21 - model_training - INFO - 模型 NeuralNet 性能: 准确率=0.8750
2025-08-08 23:09:21 - model_training - INFO - 模型 NeuralNet 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\NeuralNet_single_230921.joblib
2025-08-08 23:09:21 - model_training - INFO - 模型 NeuralNet 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NeuralNet_results.joblib
2025-08-08 23:12:11 - model_training - INFO - 模型名称: Decision Tree
2025-08-08 23:12:11 - model_training - INFO - 准确率: 0.8000
2025-08-08 23:12:11 - model_training - INFO - AUC: 0.8951
2025-08-08 23:12:11 - model_training - INFO - AUPRC: 0.7948
2025-08-08 23:12:11 - model_training - INFO - 混淆矩阵:
2025-08-08 23:12:11 - model_training - INFO - 
[[20  3]
 [ 5 12]]
2025-08-08 23:12:11 - model_training - INFO - 
分类报告:
2025-08-08 23:12:11 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.80      0.87      0.83        23
           1       0.80      0.71      0.75        17

    accuracy                           0.80        40
   macro avg       0.80      0.79      0.79        40
weighted avg       0.80      0.80      0.80        40

2025-08-08 23:12:11 - model_training - INFO - 训练时间: 0.00 秒
2025-08-08 23:12:11 - model_training - INFO - 模型 DecisionTree 性能: 准确率=0.8000
2025-08-08 23:12:11 - model_training - INFO - 模型 DecisionTree 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\DecisionTree_single_231211.joblib
2025-08-08 23:12:11 - model_training - INFO - 模型 DecisionTree 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\DecisionTree_results.joblib
2025-08-08 23:12:11 - model_training - INFO - 模型名称: Random Forest
2025-08-08 23:12:11 - model_training - INFO - 准确率: 0.8750
2025-08-08 23:12:11 - model_training - INFO - AUC: 0.9412
2025-08-08 23:12:11 - model_training - INFO - AUPRC: 0.9359
2025-08-08 23:12:11 - model_training - INFO - 混淆矩阵:
2025-08-08 23:12:11 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-08-08 23:12:11 - model_training - INFO - 
分类报告:
2025-08-08 23:12:11 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-08 23:12:11 - model_training - INFO - 训练时间: 0.08 秒
2025-08-08 23:12:11 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.8750
2025-08-08 23:12:11 - model_training - INFO - 模型 RandomForest 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\RandomForest_single_231211.joblib
2025-08-08 23:12:11 - model_training - INFO - 模型 RandomForest 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-08-08 23:12:11 - model_training - INFO - [XGBoost] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-08 23:12:11 - model_training - INFO - 模型名称: XGBoost
2025-08-08 23:12:11 - model_training - INFO - 准确率: 0.9000
2025-08-08 23:12:11 - model_training - INFO - AUC: 0.9719
2025-08-08 23:12:11 - model_training - INFO - AUPRC: 0.9627
2025-08-08 23:12:11 - model_training - INFO - 混淆矩阵:
2025-08-08 23:12:11 - model_training - INFO - 
[[21  2]
 [ 2 15]]
2025-08-08 23:12:11 - model_training - INFO - 
分类报告:
2025-08-08 23:12:11 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.91      0.91      0.91        23
           1       0.88      0.88      0.88        17

    accuracy                           0.90        40
   macro avg       0.90      0.90      0.90        40
weighted avg       0.90      0.90      0.90        40

2025-08-08 23:12:11 - model_training - INFO - 训练时间: 0.04 秒
2025-08-08 23:12:11 - model_training - INFO - 模型 XGBoost 性能: 准确率=0.9000
2025-08-08 23:12:11 - model_training - INFO - 模型 XGBoost 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\XGBoost_single_231211.joblib
2025-08-08 23:12:11 - model_training - INFO - 模型 XGBoost 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_results.joblib
2025-08-08 23:12:11 - model_training - INFO - [LightGBM] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-08 23:12:11 - model_training - INFO - 模型名称: LightGBM
2025-08-08 23:12:11 - model_training - INFO - 准确率: 0.8500
2025-08-08 23:12:11 - model_training - INFO - AUC: 0.9488
2025-08-08 23:12:11 - model_training - INFO - AUPRC: 0.9492
2025-08-08 23:12:11 - model_training - INFO - 混淆矩阵:
2025-08-08 23:12:11 - model_training - INFO - 
[[20  3]
 [ 3 14]]
2025-08-08 23:12:11 - model_training - INFO - 
分类报告:
2025-08-08 23:12:11 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.87      0.87      0.87        23
           1       0.82      0.82      0.82        17

    accuracy                           0.85        40
   macro avg       0.85      0.85      0.85        40
weighted avg       0.85      0.85      0.85        40

2025-08-08 23:12:11 - model_training - INFO - 训练时间: 0.04 秒
2025-08-08 23:12:11 - model_training - INFO - 模型 LightGBM 性能: 准确率=0.8500
2025-08-08 23:12:11 - model_training - INFO - 模型 LightGBM 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\LightGBM_single_231211.joblib
2025-08-08 23:12:11 - model_training - INFO - 模型 LightGBM 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_results.joblib
2025-08-08 23:12:11 - model_training - INFO - [CatBoost] 启用 auto_class_weights='Balanced' 以处理不平衡
2025-08-08 23:12:12 - model_training - INFO - 模型名称: CatBoost
2025-08-08 23:12:12 - model_training - INFO - 准确率: 0.8750
2025-08-08 23:12:12 - model_training - INFO - AUC: 0.9591
2025-08-08 23:12:12 - model_training - INFO - AUPRC: 0.9570
2025-08-08 23:12:12 - model_training - INFO - 混淆矩阵:
2025-08-08 23:12:12 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-08-08 23:12:12 - model_training - INFO - 
分类报告:
2025-08-08 23:12:12 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-08 23:12:12 - model_training - INFO - 训练时间: 1.04 秒
2025-08-08 23:12:12 - model_training - INFO - 模型 CatBoost 性能: 准确率=0.8750
2025-08-08 23:12:12 - model_training - INFO - 模型 CatBoost 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\CatBoost_single_231212.joblib
2025-08-08 23:12:12 - model_training - INFO - 模型 CatBoost 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\CatBoost_results.joblib
2025-08-08 23:12:12 - model_training - INFO - 模型名称: Logistic Regression
2025-08-08 23:12:12 - model_training - INFO - 准确率: 0.8250
2025-08-08 23:12:12 - model_training - INFO - AUC: 0.9284
2025-08-08 23:12:12 - model_training - INFO - AUPRC: 0.9288
2025-08-08 23:12:12 - model_training - INFO - 混淆矩阵:
2025-08-08 23:12:12 - model_training - INFO - 
[[20  3]
 [ 4 13]]
2025-08-08 23:12:12 - model_training - INFO - 
分类报告:
2025-08-08 23:12:12 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.83      0.87      0.85        23
           1       0.81      0.76      0.79        17

    accuracy                           0.82        40
   macro avg       0.82      0.82      0.82        40
weighted avg       0.82      0.82      0.82        40

2025-08-08 23:12:12 - model_training - INFO - 训练时间: 0.01 秒
2025-08-08 23:12:12 - model_training - INFO - 模型 Logistic 性能: 准确率=0.8250
2025-08-08 23:12:12 - model_training - INFO - 模型 Logistic 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\Logistic_single_231212.joblib
2025-08-08 23:12:12 - model_training - INFO - 模型 Logistic 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_results.joblib
2025-08-08 23:12:12 - model_training - INFO - 模型名称: SVM
2025-08-08 23:12:12 - model_training - INFO - 准确率: 0.7750
2025-08-08 23:12:12 - model_training - INFO - AUC: 0.9182
2025-08-08 23:12:12 - model_training - INFO - AUPRC: 0.9034
2025-08-08 23:12:12 - model_training - INFO - 混淆矩阵:
2025-08-08 23:12:12 - model_training - INFO - 
[[19  4]
 [ 5 12]]
2025-08-08 23:12:12 - model_training - INFO - 
分类报告:
2025-08-08 23:12:12 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.79      0.83      0.81        23
           1       0.75      0.71      0.73        17

    accuracy                           0.78        40
   macro avg       0.77      0.77      0.77        40
weighted avg       0.77      0.78      0.77        40

2025-08-08 23:12:12 - model_training - INFO - 训练时间: 0.01 秒
2025-08-08 23:12:12 - model_training - INFO - 模型 SVM 性能: 准确率=0.7750
2025-08-08 23:12:12 - model_training - INFO - 模型 SVM 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\SVM_single_231212.joblib
2025-08-08 23:12:12 - model_training - INFO - 模型 SVM 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\SVM_results.joblib
2025-08-08 23:12:12 - model_training - INFO - 模型名称: KNN
2025-08-08 23:12:12 - model_training - INFO - 准确率: 0.8750
2025-08-08 23:12:12 - model_training - INFO - AUC: 0.9322
2025-08-08 23:12:12 - model_training - INFO - AUPRC: 0.9189
2025-08-08 23:12:12 - model_training - INFO - 混淆矩阵:
2025-08-08 23:12:12 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-08-08 23:12:12 - model_training - INFO - 
分类报告:
2025-08-08 23:12:12 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-08 23:12:12 - model_training - INFO - 训练时间: 0.02 秒
2025-08-08 23:12:12 - model_training - INFO - 模型 KNN 性能: 准确率=0.8750
2025-08-08 23:12:12 - model_training - INFO - 模型 KNN 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\KNN_single_231212.joblib
2025-08-08 23:12:12 - model_training - INFO - 模型 KNN 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\KNN_results.joblib
2025-08-08 23:12:12 - model_training - INFO - 模型名称: Naive Bayes
2025-08-08 23:12:12 - model_training - INFO - 准确率: 0.8750
2025-08-08 23:12:12 - model_training - INFO - AUC: 0.8977
2025-08-08 23:12:12 - model_training - INFO - AUPRC: 0.9096
2025-08-08 23:12:12 - model_training - INFO - 混淆矩阵:
2025-08-08 23:12:12 - model_training - INFO - 
[[22  1]
 [ 4 13]]
2025-08-08 23:12:12 - model_training - INFO - 
分类报告:
2025-08-08 23:12:12 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.85      0.96      0.90        23
           1       0.93      0.76      0.84        17

    accuracy                           0.88        40
   macro avg       0.89      0.86      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-08 23:12:12 - model_training - INFO - 训练时间: 0.01 秒
2025-08-08 23:12:12 - model_training - INFO - 模型 NaiveBayes 性能: 准确率=0.8750
2025-08-08 23:12:12 - model_training - INFO - 模型 NaiveBayes 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\NaiveBayes_single_231212.joblib
2025-08-08 23:12:12 - model_training - INFO - 模型 NaiveBayes 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NaiveBayes_results.joblib
2025-08-08 23:12:13 - model_training - INFO - 模型名称: Neural Network
2025-08-08 23:12:13 - model_training - INFO - 准确率: 0.8750
2025-08-08 23:12:13 - model_training - INFO - AUC: 0.9591
2025-08-08 23:12:13 - model_training - INFO - AUPRC: 0.9450
2025-08-08 23:12:13 - model_training - INFO - 混淆矩阵:
2025-08-08 23:12:13 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-08-08 23:12:13 - model_training - INFO - 
分类报告:
2025-08-08 23:12:13 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-08 23:12:13 - model_training - INFO - 训练时间: 0.24 秒
2025-08-08 23:12:13 - model_training - INFO - 模型 NeuralNet 性能: 准确率=0.8750
2025-08-08 23:12:13 - model_training - INFO - 模型 NeuralNet 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\NeuralNet_single_231213.joblib
2025-08-08 23:12:13 - model_training - INFO - 模型 NeuralNet 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NeuralNet_results.joblib
