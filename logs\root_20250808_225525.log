2025-08-08 22:55:25 - hyperparameter_tuning - INFO - 开始对 CatBoost 进行超参数调优，试验次数: 5
2025-08-08 22:55:25 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 1
2025-08-08 22:55:25 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-08 22:55:25 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 5, 'n_jobs': 1}
2025-08-08 22:55:25 - hyperparameter_tuning - INFO - CatBoost强制使用CPU模式避免CUDA设备冲突
2025-08-08 22:55:29 - hyperparameter_tuning - INFO - CatBoost强制使用CPU模式避免CUDA设备冲突
2025-08-08 22:55:30 - hyperparameter_tuning - INFO - CatBoost强制使用CPU模式避免CUDA设备冲突
2025-08-08 22:55:32 - hyperparameter_tuning - INFO - CatBoost强制使用CPU模式避免CUDA设备冲突
2025-08-08 22:55:32 - hyperparameter_tuning - INFO - CatBoost强制使用CPU模式避免CUDA设备冲突
2025-08-08 22:55:33 - hyperparameter_tuning - INFO - 模型 CatBoost 的最佳参数: {'iterations': 55, 'depth': 10, 'learning_rate': 0.2514083658321223, 'l2_leaf_reg': 2.9110519961044856, 'bagging_temperature': 0.18182496720710062}
2025-08-08 22:55:33 - hyperparameter_tuning - INFO - 模型 CatBoost 的最佳得分: 0.9492
2025-08-08 22:55:33 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-08 22:55:33 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 22:55:33 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\CatBoost\optimization_history_20250808_225533.html
2025-08-08 22:55:33 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-08 22:55:33 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 22:55:33 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\CatBoost\param_importances_20250808_225533.html
2025-08-08 22:55:33 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 8.26 秒
2025-08-08 22:55:33 - hyperparameter_tuning - INFO - 开始对 NaiveBayes 进行超参数调优，试验次数: 5
2025-08-08 22:55:33 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 1
2025-08-08 22:55:33 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-08 22:55:33 - hyperparameter_tuning - INFO - NaiveBayes模型无需调参，计算基准得分
2025-08-08 22:55:33 - hyperparameter_tuning - INFO - NaiveBayes基准得分: 0.9156
