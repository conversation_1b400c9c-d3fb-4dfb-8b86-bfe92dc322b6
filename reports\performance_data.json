{"generation_time": "2025-08-08T23:10:13.111294", "best_model": "XGBoost", "best_score": 0.8898593350383632, "model_count": 10, "detailed_metrics": {"Logistic": {"accuracy": 0.825, "precision": 0.8125, "recall": 0.7647058823529411, "f1_score": 0.7878787878787878, "specificity": 0.8695652173913043, "sensitivity": 0.7647058823529411, "npv": 0.8333333333333334, "ppv": 0.8125, "auc_roc": 0.928388746803069, "auc_pr": 0.9288021844893564, "mcc": 0.6400261077368838, "kappa": 0.6391752577319587, "balanced_accuracy": 0.8171355498721227, "composite_score": 0.7995883054498452}, "SVM": {"accuracy": 0.775, "precision": 0.75, "recall": 0.7058823529411765, "f1_score": 0.7272727272727273, "specificity": 0.8260869565217391, "sensitivity": 0.7058823529411765, "npv": 0.7916666666666666, "ppv": 0.75, "auc_roc": 0.9181585677749361, "auc_pr": 0.903396518830866, "mcc": 0.5367960903599671, "kappa": 0.5360824742268041, "balanced_accuracy": 0.7659846547314578, "composite_score": 0.7442380255047042}, "NeuralNet": {"accuracy": 0.875, "precision": 0.875, "recall": 0.8235294117647058, "f1_score": 0.8484848484848485, "specificity": 0.9130434782608695, "sensitivity": 0.8235294117647058, "npv": 0.875, "ppv": 0.875, "auc_roc": 0.959079283887468, "auc_pr": 0.945048204390765, "mcc": 0.7432561251138006, "kappa": 0.7422680412371134, "balanced_accuracy": 0.8682864450127876, "composite_score": 0.8590306570062393}, "NaiveBayes": {"accuracy": 0.875, "precision": 0.9285714285714286, "recall": 0.7647058823529411, "f1_score": 0.8387096774193549, "specificity": 0.9565217391304348, "sensitivity": 0.7647058823529411, "npv": 0.8461538461538461, "ppv": 0.9285714285714286, "auc_roc": 0.89769820971867, "auc_pr": 0.9095747389865037, "mcc": 0.7474980048088188, "kappa": 0.7382198952879582, "balanced_accuracy": 0.860613810741688, "composite_score": 0.8446478747875834}, "KNN": {"accuracy": 0.875, "precision": 0.875, "recall": 0.8235294117647058, "f1_score": 0.8484848484848485, "specificity": 0.9130434782608695, "sensitivity": 0.8235294117647058, "npv": 0.875, "ppv": 0.875, "auc_roc": 0.9322250639386189, "auc_pr": 0.9188618925831202, "mcc": 0.7432561251138006, "kappa": 0.7422680412371134, "balanced_accuracy": 0.8682864450127876, "composite_score": 0.8536598130164695}, "CatBoost": {"accuracy": 0.875, "precision": 0.875, "recall": 0.8235294117647058, "f1_score": 0.8484848484848485, "specificity": 0.9130434782608695, "sensitivity": 0.8235294117647058, "npv": 0.875, "ppv": 0.875, "auc_roc": 0.959079283887468, "auc_pr": 0.9570278637770897, "mcc": 0.7432561251138006, "kappa": 0.7422680412371134, "balanced_accuracy": 0.8682864450127876, "composite_score": 0.8590306570062393}, "RandomForest": {"accuracy": 0.875, "precision": 0.875, "recall": 0.8235294117647058, "f1_score": 0.8484848484848485, "specificity": 0.9130434782608695, "sensitivity": 0.8235294117647058, "npv": 0.875, "ppv": 0.875, "auc_roc": 0.9411764705882353, "auc_pr": 0.9359102226749286, "mcc": 0.7432561251138006, "kappa": 0.7422680412371134, "balanced_accuracy": 0.8682864450127876, "composite_score": 0.8554500943463927}, "XGBoost": {"accuracy": 0.9, "precision": 0.8823529411764706, "recall": 0.8823529411764706, "f1_score": 0.8823529411764706, "specificity": 0.9130434782608695, "sensitivity": 0.8823529411764706, "npv": 0.9130434782608695, "ppv": 0.8823529411764706, "auc_roc": 0.9718670076726342, "auc_pr": 0.9626910190924032, "mcc": 0.7953964194373402, "kappa": 0.7953964194373402, "balanced_accuracy": 0.8976982097186701, "composite_score": 0.8898593350383632}, "DecisionTree": {"accuracy": 0.8, "precision": 0.8, "recall": 0.7058823529411765, "f1_score": 0.75, "specificity": 0.8695652173913043, "sensitivity": 0.7058823529411765, "npv": 0.8, "ppv": 0.8, "auc_roc": 0.8951406649616369, "auc_pr": 0.794830659536542, "mcc": 0.5875955600576713, "kappa": 0.5844155844155844, "balanced_accuracy": 0.7877237851662404, "composite_score": 0.7630498199421546}, "LightGBM": {"accuracy": 0.85, "precision": 0.8235294117647058, "recall": 0.8235294117647058, "f1_score": 0.8235294117647058, "specificity": 0.8695652173913043, "sensitivity": 0.8235294117647058, "npv": 0.8695652173913043, "ppv": 0.8235294117647058, "auc_roc": 0.948849104859335, "auc_pr": 0.9491847265221878, "mcc": 0.6930946291560103, "kappa": 0.6930946291560103, "balanced_accuracy": 0.8465473145780051, "composite_score": 0.8329987212276215}}, "ranking": [["XGBoost", 0.8898593350383632], ["NeuralNet", 0.8590306570062393], ["CatBoost", 0.8590306570062393], ["RandomForest", 0.8554500943463927], ["KNN", 0.8536598130164695], ["<PERSON><PERSON><PERSON><PERSON><PERSON>", 0.8446478747875834], ["LightGBM", 0.8329987212276215], ["Logistic", 0.7995883054498452], ["DecisionTree", 0.7630498199421546], ["SVM", 0.7442380255047042]]}